import {FC} from "react";
import WaitingForOpponentPage
  from "@/src/client/Gaming/infrastructure/pages/Gaming/WaitingForOpponentPage/WaitingForOpponentPage";

type Props = {
  params: Promise<{
    locale: string,
    gameId: string;
  }>;
};

const WaitingForOpponentPageContainer: FC<Props> = async ({params}: Props) => {
  const {gameId, locale} = await params;

  return (
    <WaitingForOpponentPage gameId={gameId} locale={locale}/>
  );
};

export default WaitingForOpponentPageContainer;