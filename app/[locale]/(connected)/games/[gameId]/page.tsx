import {FC} from "react";
import GameDetailsPage from "@/src/client/Gaming/infrastructure/pages/Gaming/GameDetailsPage/GameDetailsPage";

type Props = {
  params: Promise<{ gameId: string; locale: string }>;
};

const GamePage: FC<Props> = async ({params}: Props) => {
  const {gameId, locale} = await params;

  return (
    <GameDetailsPage locale={locale} gameId={gameId}/>
  );
};

export default GamePage;