import {FC} from "react";
import PlayGamePageWithQueueCheck from "@/src/client/Gaming/infrastructure/pages/Gaming/PlayGamePage/PlayGamePageWithQueueCheck";

type Props = {
  params: Promise<{ locale: string, gameId: string; }>;
};

const PlayGamePageContainer: FC<Props> = async ({params}: Props) => {
  const {gameId, locale} = await params;

  return (
    <PlayGamePageWithQueueCheck gameId={gameId} locale={locale}/>
  );
};

export default PlayGamePageContainer;
