Overview
- Decks are player-owned collections tied to a game. They store catalog card IDs with quantities.
- GameDecks are per-match expansions of a Deck into unique, per-instance GameCards (one instance per quantity) with frozen card data.
- Players select a Deck to enter matchmaking; the selected deckId flows through the queue and into match setup. GameDeck creation is intended to be server-side and persisted for gameplay queries.

Key Domain Models
- Deck (server/DeckBuilding/domain/Deck/Deck.ts): id, gameId, playerId, name, tags, cards[{cardId, quantity}]. Provides create/fromSnapshot/toSnapshot and basic behaviors (rename/useTags/useCards/isOwnedBy).
- GameDeck (server/Gaming/domain/GameDeck/GameDeck.ts): id, gameId, playerId, cards[GameCard]. Snapshotable; used during games.
- GameCard (server/Gaming/domain/GameCard/GameCard.ts): id, catalogCardId, data(CardData). Data is copied from Catalog at creation time to decouple gameplay from later catalog edits.

Repositories (Ports + Implementations)
- DeckRepository (server/DeckBuilding/application/ports/DeckRepository.ts)
  - Implementations: ConvexDeckRepository (Convex), InMemoryDeckRepository (tests).
- CatalogCardListRepository (server/DeckBuilding/application/ports/CatalogCardListRepository.ts)
  - Implementations: ConvexCatalogCardListRepository (Convex), InMemoryCatalogCardListRepository (tests).
- GameDeckRepository (server/Gaming/application/ports/GameDeckRepository.ts)
  - Implementations: InMemoryGameDeckRepository (tests). Convex implementation missing (see TODO below).

Creating Decks (Client)
- Command: src/client/DeckBuilding/application/commands/autoSaveDeck/autoSaveDeck.ts
  - Builds cards payload from deckBuilder state.
  - If deckId exists -> update via DeckService; else -> create via DeckService, dispatch deckCreatedEvent, and navigate to the new deck URL.
  - Prevents empty-deck saves and duplicate concurrent creations.
- Load Deck into builder: src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts
  - Hydrates deckBuilder state with catalog cards referenced by the deck.

Selecting a Deck to Play (Client)
- Deck list state: src/client/Gaming/domain/DeckSelection/deckSelectionReducer.ts
  - Events: decksLoadingStarted/Loaded/Failed, deckSelected. Auto-selects first deck when none is selected.
- Load decks for game: src/client/Gaming/application/commands/loadDecksForGame/loadDecksForGame.ts
  - Uses DeckListService (ConvexDeckListService) to fetch user decks for a given game.
- UI hook: src/client/Gaming/infrastructure/pages/Gaming/PlayGamePage/usePlayGamePage.tsx
  - Loads decks on mount, exposes selected deck, dispatches joinMatchMakingQueue(gameId, selectedDeckId) on start.

Entering Matchmaking (Client -> Server)
- Client service: src/client/Gaming/infrastructure/services/MatchMaking/ConvexMatchMakingService.ts
  - joinMatchMakingQueue(gameId, deckId) -> convex mutation api.mutations.addPlayerToMatchMakingQueue.
  - cancelMatchRegistration(gameId) -> convex mutation api.mutations.cancelMatchRegistration.
- Server command: src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts
  - Validates not already queued; saves MatchmakingQueueItem {gameId, deckId, playerId, queuedAt}; emits PlayerAddedToMatchMakingQueue.

Building a Match (Server)
- MakeMatch: src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts
  - Selects an opponent, creates a Match with status 'setup', emits MatchCreated with players.
  - Note: This step currently does not instantiate GameDecks.
- Sagas: convex/dispatchers/sagas
  - playerAddedToMatchMakingQueueSaga: triggers MakeMatch and updates status to waiting-for-opponent.
  - matchCreatedSaga: cleans up queue and sets players to playing. Does not create GameDecks yet.

Creating a GameDeck (Server)
- Command + Handler: src/server/Gaming/application/commands/GameDeck/CreateGameDeck/CreateGameDeckCommandHandler.ts
  - Input: deckId (the chosen player deck).
  - Flow:
    1) deckRepository.findById(deckId) -> throws 'Deck not found' if missing.
    2) catalogRepository.getByGameId(deck.gameId).
    3) For each {cardId, quantity} in deck.cards, look up CatalogCard; for each copy push a new GameCard with a generated id (gc1, gc2, ...) and a deep copy of card.data.
    4) GameDeck.create({gameId, playerId, cards}) -> gameDeckRepository.save -> returns gameDeckId.
  - Rationale: Duplicates deck composition into immutable per-game instances so gameplay state does not mutate the catalog or builder deck.

Querying a GameDeck (Server)
- Query + Handler: src/server/Gaming/application/queries/LoadGameDeckById/LoadGameDeckByIdQueryHandler.ts
  - repository.findById(deckId) -> presenter.display(deck) or presenter.displayError(Error('Deck not found')).
  - Presenter Port: src/server/Gaming/application/ports/LoadGameDeckByIdPresenter.ts (web presenter not wired).

Game Start and Scene Initialization (Client)
- initializeGameScene: src/client/Gaming/application/commands/initializeGameScene/initializeGameScene.ts
  - Calls GameService.loadMatchData(matchId) then dispatches updatePlayerHand for both players.
  - ConvexGameService currently returns empty arrays with a TODO to integrate GameDeck once available in Match data.

Data Flow Summary
- Authoring: Player composes Decks in DeckBuilder and persists them via DeckService.
- Selection: PlayGamePage loads decks for the current game and lets the player pick one.
- Queue: Client submits (gameId, deckId) to matchmaking; server enqueues a MatchmakingQueueItem.
- Match: Server pairs players and creates Match with status 'setup'.
- GameDeck: For each player deckId, the server should run CreateGameDeckCommand to create a persisted GameDeck with per-instance GameCards.
- Gameplay: Client initializes the scene by loading match and associated GameDeck(s) to derive starting hands.

Integration Gaps / Next Steps
- Wire GameDeck creation into matchmaking:
  - On MatchCreated, resolve both players' deckIds from the queued items and run CreateGameDeckCommand for each.
  - Persist the relation in Match (e.g., add field playerGameDecks: [{playerId, gameDeckId}]) or a separate mapping.
- Expose GameDeck via API:
  - Add convex functions/queries to load a GameDeck by id and to fetch a match with its players' gameDeckIds.
- Update GameService.loadMatchData:
  - Use matchId -> fetch players and their gameDeckIds, then query those GameDecks and map to hands for initializeGameScene.
- UI/State:
  - When navigating to the match, call initializeGameScene with matchId so hands are populated from GameDeck(s).
- Testing:
  - Unit: add tests for match -> gameDeck creation flow (two players, deck lookups, catalog copies, error branches).
  - Integration: end-to-end from join queue -> match created -> game decks persisted -> client initializes scene with non-empty hands.

Step-by-Step TODO: Fully Wire initializeGameScene With Player GameDecks

1) Convex Schema
- Add table `gameDecks` in `convex/schema.ts`: `{ gameId: v.id('games'), matchId: v.id('matches'), playerId: v.string(), cards: v.array(v.object({ id: v.string(), catalogCardId: v.string(), data: v.any() })), createdAt: v.number() }`.
- Add indices: `by_matchId` on `matchId`, optionally `by_matchId_and_playerId`.
- Extend `matches` with `playerGameDecks: v.array(v.object({ playerId: v.string(), gameDeckId: v.id('gameDecks') }))` to link both players' GameDecks.

2) GameDeck Repository (Convex)
- Create `src/server/Gaming/infrastructure/repositories/GameDeck/ConvexGameDeckRepository.ts`:
  - `save(deck: GameDeck): Promise<string>` inserts/patches into `gameDecks` (mutation ctx) and returns `_id`.
  - `findById(id: string): Promise<GameDeck|null>` reads from `gameDecks` (query ctx) and maps to domain (`GameDeck.fromSnapshot`).
- Optionally split read/write into `ConvexGameDeckReadRepository` and `ConvexGameDeckRepository` for strict ctx types.

3) Wire GameDeck Creation on Match Created
- Location: `convex/dispatchers/sagas/matchCreatedSaga.ts`.
- Steps inside saga after match creation:
  - Load the matchmaking queue items for the two players to retrieve their `deckId`s (use `ConvexMatchmakingQueueRepository.findByGameId` and filter with `findItemsForPlayers`).
  - For each `{playerId, deckId}` run `CreateGameDeckCommandHandler` using Convex-backed repos: `ConvexDeckRepository`, `ConvexCatalogCardListRepository`, `ConvexGameDeckRepository`.
  - Collect the two returned `gameDeckId`s and patch the `matches` document with `playerGameDecks: [{playerId, gameDeckId}, {playerId, gameDeckId}]` via `ConvexMatchRepository`.
  - Proceed with existing cleanup and status updates.

4) Expose Match + GameDeck Data via Convex Query
- Add `convex/queries/gaming/loadMatchData.ts` (or enhance `convex/queries/match/loadMatchById.ts`):
  - Input: `{ matchId: v.string() }`, user from `ctx`.
  - Load match by id (using `ConvexMatchReadRepository`).
  - Read `playerGameDecks` and fetch the caller's GameDeck and the opponent's GameDeck via `ConvexGameDeckReadRepository`.
  - Build `{ player1Cards: string[], player2Cards: string[] }` where player1 is the caller. For opponent, either return placeholders of equal length (preferred) or full IDs and let the client only use count.
  - Return a view model shaped for `ConvexGameService.loadMatchData`.

5) Client: Use Real Match Data
- Update `src/client/Gaming/infrastructure/services/Game/ConvexGameService.ts`:
  - Implement `loadMatchData(matchId)` to call the new Convex query and return `{ player1Cards, player2Cards }` from its view model.
- Keep `initializeGameScene` unchanged; it already dispatches `updatePlayerHand` for both players.

6) Authorization & Guards
- Ensure the query checks the requester is one of the match players before revealing any data; otherwise return an error.
- In the saga, validate the `deckId` owner matches the `playerId`.

7) Tests (TDD)
- Server unit: add tests for ConvexGameDeckRepository save/find, error paths.
- Server integration (Convex):
  - Player joins queue -> `playerAddedToMatchMakingQueueSaga` triggers -> `MakeMatch` creates match -> `matchCreatedSaga` creates two GameDecks and patches the match -> queue cleanup -> statuses updated.
  - New query `loadMatchData` returns real player1 cards and opponent length.
- Client unit:
  - `ConvexGameService.loadMatchData` transforms the query result correctly.
  - `initializeGameScene` updates player1 hand with card IDs and player2 count based on result.

8) Rollout Order
- Land schema changes.
- Implement ConvexGameDeckRepository.
- Implement saga wiring in `matchCreatedSaga`.
- Add/load query for match data.
- Update client `ConvexGameService` and verify `initializeGameScene` shows non-empty hands for player1 and correct opponent count.

