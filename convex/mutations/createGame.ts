import {v} from 'convex/values';
import {protectedMutation} from '@/convex/helpers';
import {ConvexGameRepository} from '@/src/server/GameManagement/infrastructure/repositories/Game/ConvexGameRepository';
import {CreateGameCommandHandler} from '@/src/server/GameManagement/application/commands/CreateGame/CreateGameCommandHandler';

export const endpoint = protectedMutation({
  args: {name: v.string()},
  handler: async (ctx, {name}) => {
    const repository = new ConvexGameRepository(ctx);
    const handler = new CreateGameCommandHandler(repository);
    return handler.handle({ownerId: ctx.userId, name});
  },
});
