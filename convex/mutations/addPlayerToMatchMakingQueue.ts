import {v} from "convex/values";
import {protectedMutation} from "@/convex/helpers";
import {AddPlayerToMatchMakingQueueCommandHandler} from "@/src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler";
import {ConvexMatchmakingQueueRepository} from "@/src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository";
import {ConvexEventBus} from "@/src/server/Shared/infrastructure/gateways/Context/ConvexEventBus";
import {RealTimeService} from "@/src/server/Shared/infrastructure/providers/Time/RealTimeService";

export const endpoint = protectedMutation({
  args: {
    gameId: v.string(),
    deckId: v.string(),
  },
  handler: async (ctx, {gameId, deckId}) => {
    const repository = new ConvexMatchmakingQueueRepository(ctx);
    const eventBus = new ConvexEventBus(ctx);
    const commandHandler = new AddPlayerToMatchMakingQueueCommandHandler(
      eventBus,
      repository,
      new RealTimeService()
    );
    return commandHandler.handle({gameId: gameId, deckId, userId: ctx.userId});
  },
});
