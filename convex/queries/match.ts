import {LoadMatchByIdWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchByIdWebPresenter";
import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {
  ConvexMatchReadRepository
} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchReadRepository";
import {
  LoadMatchByIdQueryHandler
} from "@/src/server/Gaming/application/queries/LoadMatchById/LoadMatchByIdQueryHandler";
import {
  ConvexGameDeckReadRepository
} from "@/src/server/Gaming/infrastructure/repositories/GameDeck/ConvexGameDeckReadRepository";
import {LoadMatchDataWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchDataWebPresenter";
import {
  ConvexCatalogCardListRepository
} from "@/src/server/DeckBuilding/infrastructure/repositories/CatalogCardList/ConvexCatalogCardListRepository";
import {
  LoadMatchDataQueryHandler
} from "@/src/server/Gaming/application/queries/LoadMatchData/LoadMatchDataQueryHandler";
import {
  ConvexMatchEventListRepository
} from "@/src/server/Gaming/infrastructure/repositories/MatchEventList/ConvexMatchEventListRepository";
import {
  LoadMatchEventsQueryHandler
} from "@/src/server/Gaming/application/queries/LoadMatchEvents/LoadMatchEventsQueryHandler";
import {LoadMatchEventsWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchEventsWebPresenter";
import {
  ConvexMatchEndedEventRepository
} from "@/src/server/Gaming/infrastructure/repositories/MatchEndedEvent/ConvexMatchEndedEventRepository";
import {
  LoadMatchEndedEventQueryHandler
} from "@/src/server/Gaming/application/queries/LoadMatchEndedEvent/LoadMatchEndedEventQueryHandler";
import {
  LoadMatchEndedEventWebPresenter
} from "@/src/server/Gaming/presentation/presenters/LoadMatchEndedEventWebPresenter";

export const loadMatchById = protectedQuery({
  args: {
    matchId: v.string(),
  },
  handler: async (ctx, {matchId}) => {
    const repository = new ConvexMatchReadRepository(ctx);
    const handler = new LoadMatchByIdQueryHandler(repository);
    const presenter = new LoadMatchByIdWebPresenter();

    await handler.handle({matchId, userId: ctx.userId}, presenter);

    return presenter.getViewModel();
  },
});

export const loadMatchData = protectedQuery({
  args: { matchId: v.string() },
  handler: async (ctx, { matchId }) => {
    const matchRepo = new ConvexMatchReadRepository(ctx);
    const deckRepo = new ConvexGameDeckReadRepository(ctx);
    const presenter = new LoadMatchDataWebPresenter();
    const catalogRepo = new ConvexCatalogCardListRepository(ctx);
    const handler = new LoadMatchDataQueryHandler(matchRepo, deckRepo, catalogRepo);

    await handler.handle({ matchId, userId: ctx.userId }, presenter);
    return presenter.getViewModel();
  }
});

export const matchEvents = protectedQuery({
  args: {
    matchId: v.id("matches"),
  },
  handler: async (ctx, {matchId}) => {
    const repository = new ConvexMatchEventListRepository(ctx);
    const handler = new LoadMatchEventsQueryHandler(repository);
    const presenter = new LoadMatchEventsWebPresenter();

    await handler.handle({matchId}, presenter);

    return presenter.getViewModel();
  },
});

export const onMatchEnded = protectedQuery({
  args: {
    matchId: v.id("matches"),
  },
  handler: async (ctx, {matchId}) => {
    const repository = new ConvexMatchEndedEventRepository(ctx);
    const queryHandler = new LoadMatchEndedEventQueryHandler(repository);
    const presenter = new LoadMatchEndedEventWebPresenter();

    await queryHandler.handle({matchId}, presenter);

    return presenter.getViewModel();
  },
});
