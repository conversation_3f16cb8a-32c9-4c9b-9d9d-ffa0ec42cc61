import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {ConvexMatchReadRepository} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchReadRepository";
import {ConvexGameDeckReadRepository} from "@/src/server/Gaming/infrastructure/repositories/GameDeck/ConvexGameDeckReadRepository";
import {LoadMulliganDataQueryHandler} from "@/src/server/Gaming/application/queries/LoadMulliganData/LoadMulliganDataQueryHandler";
import {LoadMulliganDataWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMulliganDataWebPresenter";
import {ConvexCatalogCardListRepository} from "@/src/server/DeckBuilding/infrastructure/repositories/CatalogCardList/ConvexCatalogCardListRepository";
import {ConvexGameSettingsRepository} from "@/src/server/Gaming/infrastructure/repositories/GameSettings/ConvexGameSettingsRepository";
import {ConvexMulliganSelectionReadRepository} from "@/src/server/Gaming/infrastructure/repositories/MulliganSelection/ConvexMulliganSelectionReadRepository";

export const endpoint = protectedQuery({
  args: { matchId: v.string() },
  handler: async (ctx, { matchId }) => {
    const matchRepo = new ConvexMatchReadRepository(ctx);
    const deckRepo = new ConvexGameDeckReadRepository(ctx);
    const catalogRepo = new ConvexCatalogCardListRepository(ctx);
    const gameSettingsRepo = new ConvexGameSettingsRepository(ctx);
    const mulliganRepo = new ConvexMulliganSelectionReadRepository(ctx);
    const presenter = new LoadMulliganDataWebPresenter();
    const handler = new LoadMulliganDataQueryHandler(matchRepo, deckRepo, catalogRepo, gameSettingsRepo, mulliganRepo);

    await handler.handle({ matchId, userId: ctx.userId }, presenter);
    return presenter.getViewModel();
  }
});