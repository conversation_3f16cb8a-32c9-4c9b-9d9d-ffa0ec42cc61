import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {
  ConvexGameSettingsRepository
} from "@/src/server/Gaming/infrastructure/repositories/GameSettings/ConvexGameSettingsRepository";
import {
  LoadGameSettingsByGameIdQueryHandler
} from "@/src/server/Gaming/application/queries/LoadGameSettingsByGameId/LoadGameSettingsByGameIdQueryHandler";
import {
  LoadGameSettingsByGameIdWebPresenter
} from "@/src/server/Gaming/presentation/presenters/LoadGameSettingsByGameIdWebPresenter";

export const loadGameSettingsByGameId = protectedQuery({
  args: {
    gameId: v.id("games"),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexGameSettingsRepository(ctx);
    const queryHandler = new LoadGameSettingsByGameIdQueryHandler(repository);
    const presenter = new LoadGameSettingsByGameIdWebPresenter();

    await queryHandler.handle({gameId}, presenter);

    return presenter.getViewModel();
  },
});