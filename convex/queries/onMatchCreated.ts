import {v} from "convex/values";
import {protectedQuery} from "@/convex/helpers";
import {LoadMatchCreatedEventQueryHandler} from "@/src/server/Gaming/application/queries/LoadMatchCreatedEvent/LoadMatchCreatedEventQueryHandler";
import {ConvexMatchMakingEventRepository} from "@/src/server/Gaming/infrastructure/repositories/MatchMakingEvent/ConvexMatchMakingEventRepository";
import {ConvexMatchReadRepository} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchReadRepository";
import {LoadMatchCreatedEventWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchCreatedEventWebPresenter";

export const endpoint = protectedQuery({
  args: {
    gameId: v.id("games"),
  },
  handler: async (ctx, { gameId }) => {
    const queryHandler = new LoadMatchCreatedEventQueryHandler(
      new ConvexMatchMakingEventRepository(ctx),
      new ConvexMatchReadRepository(ctx)
    );
    const presenter = new LoadMatchCreatedEventWebPresenter();

    await queryHandler.handle({gameId, userId: ctx.userId}, presenter);

    return presenter.getViewModel();
  },
});
