import {protectedQuery} from "../helpers";
import {v} from "convex/values";
import {LoadGameByIdQueryHandler} from "@/src/server/Gaming/application/queries/LoadGameById/LoadGameByIdQueryHandler";
import {ConvexGameRepository} from "@/src/server/Gaming/infrastructure/repositories/Game/ConvexGameRepository";
import {LoadGameByIdWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadGameByIdWebPresenter";

export const endpoint = protectedQuery({
  args: {
    gameId: v.string(),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexGameRepository(ctx);
    const queryHandler = new LoadGameByIdQueryHandler(repository);
    const presenter = new LoadGameByIdWebPresenter();

    await queryHandler.handle({gameId}, presenter);

    return presenter.getViewModel();
  },
});
