import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {Id} from "@/convex/_generated/dataModel";

export const endpoint = protectedQuery({
  args: { matchId: v.string() },
  handler: async (ctx, { matchId }) => {
    // Get the match
    const match = await ctx.db.get(matchId as Id<"matches">);
    if (!match) {
      return { error: "Match not found", match: null, gameDecks: [], userId: ctx.userId };
    }

    // Get all gameDecks for this game
    const gameDecks = await ctx.db
      .query('gameDecks')
      .withIndex('by_gameId', q => q.eq('gameId', match.gameId))
      .collect();

    // Filter for current user
    const userGameDecks = gameDecks.filter(d => d.playerId === ctx.userId);

    return {
      error: null,
      match: {
        id: match._id,
        gameId: match.gameId,
        status: match.status,
        players: match.players,
        playerGameDecks: match.playerGameDecks
      },
      allGameDecks: gameDecks.map(d => ({ 
        id: d._id, 
        gameId: d.gameId, 
        playerId: d.playerId, 
        cardCount: d.cards.length 
      })),
      userGameDecks: userGameDecks.map(d => ({ 
        id: d._id, 
        gameId: d.gameId, 
        playerId: d.playerId, 
        cardCount: d.cards.length,
        firstFewCards: d.cards.slice(0, 3)
      })),
      userId: ctx.userId
    };
  }
});