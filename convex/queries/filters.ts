import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {
  ConvexGameFilterListRepository
} from "@/src/server/Gaming/infrastructure/repositories/GameFilterList/ConvexGameFilterListRepository";
import {
  LoadGameFilterListQueryHandler
} from "@/src/server/Gaming/application/queries/LoadGameFilterList/LoadGameFilterListQueryHandler";
import {
  LoadGameFilterListWebPresenter
} from "@/src/server/Gaming/presentation/presenters/LoadGameFilterListWebPresenter";

export const loadAvailableFilters = protectedQuery({
  args: {
    gameId: v.string(),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexGameFilterListRepository(ctx);
    const queryHandler = new LoadGameFilterListQueryHandler(repository);
    const presenter = new LoadGameFilterListWebPresenter();

    await queryHandler.handle({gameId}, presenter);

    return presenter.getViewModel();
  },
});
