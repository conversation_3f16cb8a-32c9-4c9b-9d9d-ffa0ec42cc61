import {protectedQuery} from "@/convex/helpers";

export const endpoint = protectedQuery({
  args: {},
  handler: async (ctx) => {
    const matches = await ctx.db
      .query('matches')
      .filter(q => 
        q.or(
          q.eq(q.field('status'), 'setup'),
          q.eq(q.field('status'), 'playing')
        )
      )
      .collect();

    const activeMatch = matches.find(match => 
      match.players.includes(ctx.userId)
    );

    if (!activeMatch) {
      return {hasActiveMatch: false, matchId: null, gameId: null};
    }

    return {
      hasActiveMatch: true,
      matchId: activeMatch._id,
      gameId: activeMatch.gameId,
      status: activeMatch.status
    };
  },
});