import {protectedQuery} from "../helpers";
import {ConvexGameListRepository} from "@/src/server/Gaming/infrastructure/repositories/GameList/ConvexGameListRepository";
import {LoadGameListWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadGameListWebPresenter";
import {LoadGameListQueryHandler} from "@/src/server/Gaming/application/queries/LoadGameList/LoadGameListQueryHandler";

export const endpoint = protectedQuery({
  args: {},
  handler: async (ctx) => {
    const repository = new ConvexGameListRepository(ctx);
    const presenter = new LoadGameListWebPresenter();
    const queryHandler = new LoadGameListQueryHandler(repository);
    await queryHandler.handle({}, presenter);
    return presenter.getViewModel();
  },
});
