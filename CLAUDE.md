# Claude Code Configuration for EvyGames

This file contains specific guidance and conventions for Claude Code when working with the EvyGames project, a Next.js 15 + Convex application built with Clean Architecture + DDD + CQRS + TDD.

## Project Overview

**EvyGames** is a comprehensive card game platform featuring:
- **Architecture**: Clean Architecture + Domain-Driven Design + CQRS + Test-Driven Development
- **Frontend**: Next.js 15, React 19, Radix UI, Tailwind CSS, Three.js for 3D gaming
- **Backend**: Convex for real-time backend services
- **Bounded Contexts**: Authentication, CatalogManagement, DeckBuilding, GameManagement, Gaming, MatchMaking, Shared

## Essential Claude Code Commands

### Development Workflow Commands

```bash
# Start development with both Next.js and Convex
npm run dev:all

# Run linting and type checking (MUST pass before commits)
npm run lint

# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Build the application
npm build

# Convex-specific commands
npm run convex:dev
npm run convex:deploy
```

### Quality Assurance Commands

```bash
# Lint with auto-fix
npm run lint:fix

# Run specific test file
npm test -- path/to/test.spec.ts

# Open Cypress for E2E testing
npm run cy:local

# Run Cypress in CI mode
npm run cy:ci

# Generate dependency graphs
npm run deps:all
```

## Project Structure & Navigation

### Bounded Context Architecture

```
src/
├── client/                     # Frontend bounded contexts
│   ├── Authentication/         # User auth, sign-in
│   ├── CatalogManagement/      # Admin card catalog management  
│   ├── DeckBuilding/          # Deck creation and editing
│   ├── GameManagement/        # Admin game configuration
│   ├── Gaming/               # Match gameplay and 3D rendering
│   ├── MatchMaking/          # Player queuing and matching
│   └── Shared/               # Cross-cutting concerns
└── server/                    # Backend bounded contexts
    ├── Authentication/        # Server-side auth logic
    ├── CatalogManagement/     # Card catalog domain logic
    ├── DeckBuilding/         # Deck persistence and validation
    ├── GameManagement/       # Game configuration logic
    ├── Gaming/              # Match state management
    ├── MatchMaking/         # Queue management and matching
    └── Shared/              # Server utilities and DI
```

### Layer Structure (within each bounded context)

```
BoundedContext/
├── application/           # Use cases (commands & queries)
│   ├── commands/         # Write operations (CQRS commands)
│   ├── queries/         # Read operations (CQRS queries)
│   ├── ports/           # Interfaces/contracts
│   └── services/        # Application services
├── domain/              # Core business logic
│   ├── [Entity]/        # Domain entities
│   ├── [ValueObject]/   # Value objects
│   └── events/          # Domain events
├── infrastructure/      # External concerns
│   ├── components/      # React components (client only)
│   ├── repositories/    # Data access implementations
│   ├── services/       # External service implementations
│   └── hooks/          # React hooks (client only)
├── presentation/        # Interface adapters (server only)
│   ├── presenters/     # Output formatters
│   └── viewModels/     # Response DTOs
└── specs/              # Tests
    ├── unit/           # Unit tests
    ├── integration/    # Integration tests
    └── components/     # Component tests
```

## Claude Code Best Practices for This Project

### 1. File Navigation Strategy

**Start with Context Identification:**
```bash
# Use LS to explore bounded contexts
LS src/client
LS src/server

# Navigate to specific context
LS src/client/DeckBuilding

# Find specific files with Grep
Grep -r "addCardToDeck" src/client/DeckBuilding
```

**Common File Location Patterns:**
- Commands: `application/commands/[action]/[action]Command.ts`
- Queries: `application/queries/[action]/[action]Query.ts`
- Components: `infrastructure/components/app/[Context]/[Component]/`
- Tests: `specs/unit/commands/[action].spec.ts`

### 2. CQRS Implementation Patterns

**Adding a New Command:**
1. Create command interface: `[Action]Command.ts`
2. Create handler: `[Action]CommandHandler.ts`
3. Create thunk: `[action].ts`
4. Create request type: `[action]Request.ts`
5. Write tests: `specs/unit/commands/[action].spec.ts`

**Adding a New Query:**
1. Create selector: `queries/[action]/[action].ts`
2. Write tests: `specs/unit/queries/[action].spec.ts`

### 3. Code Quality Standards

**Documentation and Naming:**
- **No documentation blocks** of any kind are allowed in the codebase (tests included)
- Write **self-documenting code** through expressive naming and clear structure
- **No comments in production code** except for the three-section test structure (`// Arrange`, `// Act`, `// Assert`)
- Use **full, descriptive names** without abbreviations (e.g., `Authentication` not `Auth`, `UserRepository` not `UserRepo`)

**Design Principles (Strict Adherence Required):**
- **Single Responsibility Principle**: Each class/function should have exactly one reason to change
- **Open/Closed Principle**: Open for extension, closed for modification
- **Liskov Substitution Principle**: Derived classes must be substitutable for their base classes
- **Interface Segregation Principle**: Clients should not depend on interfaces they don't use
- **Dependency Inversion Principle**: Depend on abstractions, not concretions
- **Tell Don't Ask**: Objects should tell other objects what to do, not ask for data to act upon
- **Law of Demeter**: Only talk to immediate friends, avoid chaining method calls
- **Extract until drop**: Continuously extract functions, variables, and classes until no further meaningful extraction is possible

**Code Philosophy:**
- **Focus on behavior over implementation**: Express what the code does, not how it does it
- **Prioritize readability over DRY**: Don't repeat yourself only when it improves readability and maintainability
- **Keep It Simple (KISS)**: Simple means readable, maintainable, and testable - not naive or oversimplified
- **Encapsulate external dependencies**: Always wrap third-party libraries in your own abstractions
- **Use DTOs (Data Transfer Objects)** for transferring data between layers
- **Avoid getter and setter methods**: Always prefer behavior methods over exposing internal state

### 4. Test-Driven Development (TDD) Requirements

**TDD Workflow:**
- **Red-Green-Refactor cycle**: Write a failing test → Make it pass → Refactor
- **Tests first, always**: A failing test must precede every line of production code
- **Triangulation**: Write the minimum code necessary to make tests pass, then add more tests to drive out the real implementation

**Pair Programming:**
- Either you or the user can start the pair programming session
- Implement features in ping pong style: one writes the test, the other implements minimum code to make it pass
- Wait for the next cycle after refactoring discussions

**Test Quality Standards:**
- **Comprehensive coverage**: Test all branches, not just happy paths
- **Parameter combinations**: Test all meaningful combinations of input parameters
- **Exception handling**: Test all error conditions and edge cases
- **Specification-style tests**: Tests should read like executable specifications of behavior
- **Isolated and independent**: Tests must run in isolation, in any order and be repeatable
- **Fast execution**: Tests should run in milliseconds, not seconds
- **No logic in tests**: Tests should only assert behavior, always prefer splitting into multiple tests rather than if/else statements
- **it** should never contain "when": Always use `describe` for context

**Test Structure (MANDATORY):**
```typescript
describe("When [context/action]", () => {
  it("should [expected behavior]", async () => {
    // Arrange
    const setup = createTestingEnvironment();
    
    // Act  
    const result = await handler.execute(command);
    
    // Assert
    expect(result).toEqual(expectedResult);
  });
});
```

**Key Testing Rules:**
- Use sociable tests through command/query handlers
- Never test domain objects directly
- One action per test in `// Act` section (most of the time it is only one line of code)
- Start all `it` statements with "should"
- Use `describe("When ...")` for context
- `it` cannot contain behavioral context, only simple functional intention
- Avoid `and`/`or` in single `it`, split in different `it` instead or occasionally use parameterized tests
- Never leave `.only` or `.skip` in committed tests. Ask the user if you need them.
- **CRITICAL**: Only `// Arrange`, `// Act`, `// Assert` comments allowed - NO OTHER COMMENTS

**Mocking Strategy:**
- **Don't mock what you don't own**: Use dependency injection whenever possible rather than extensive mocking
- **Use cases never use jest or vitest mocks**: Mocks are only used in rare cases and never in use cases which use in memory or fake implementations
- **Automatic execution**: All tests must be executable via `npm test`

**Code Justification:**
- **Every line must be justified**: If you cannot explain why a line of code exists in the new code by a relevant test, remove it
- **No speculative generality**: Don't add functionality that isn't immediately needed
- **Behavior-driven**: Code should express business requirements and domain concepts clearly

**Legacy Code Testing:**
- When dealing with legacy code, you always have to put a test harness
- Tests should not focus on technical details but on behaviors
- Don't rely on unstable things like css classes or ids
- Use data-testid to select DOM elements
- Don't verify volatile data (css, classes, styles for example)

### 5. Technology Stack Integration

**Frontend Technologies:**
- **React 19** with Next.js 15
- **Redux Toolkit** for state management
- **Radix UI** for component library
- **Tailwind CSS** for styling
- **Three.js/@react-three/fiber** for 3D gaming
- **Framer Motion** for animations

**Backend Technologies:**
- **Convex** for real-time backend
- **TypeScript** throughout
- **Custom ESLint rules** for architecture enforcement

### 6. Code Quality Standards

**Before Every Commit:**
```bash
npm run lint    # MUST pass
npm test       # MUST pass
```

**Import Conventions:**
- Always use `@/` path alias for absolute imports
- Import from interfaces, not implementations
- Follow dependency direction: domain ← application ← infrastructure

**Architecture Boundaries:**
- ESLint rules enforce Clean Architecture boundaries
- Never import from higher layers
- Use dependency injection for cross-layer communication

### 7. Convex Integration

**Convex Structure:**
```
convex/
├── mutations/           # Write operations
├── queries/            # Read operations  
├── schema.ts          # Database schema
├── auth.ts           # Authentication config
└── dispatchers/      # Event handling
```

**Common Patterns:**
- Mutations map to CQRS commands
- Queries map to CQRS queries
- Use dependency injection for service access

### 8. Component Development

**Component Structure:**
```
ComponentName/
├── ComponentName.tsx      # Main component (humble object)
├── useComponentName.ts    # Hook for logic integration
└── ComponentName.spec.tsx # Component tests
```

**React Best Practices:**
- Keep components humble (no business logic)
- Use hooks as glue between components and use cases
- Dispatch thunks for actions, use selectors for state

## Claude Code Tool Usage Examples

### Effective File Navigation

```bash
# Start broad - explore bounded contexts
LS src/client

# Navigate to specific context for feature work
LS src/client/DeckBuilding/application/commands

# Find specific implementations
Grep "addCardToDeck" src/client/DeckBuilding --output_mode content

# Read existing patterns before creating new ones
Read src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts
Read src/client/DeckBuilding/specs/unit/commands/addCardToDeck.spec.ts
```

### Multi-Tool Workflows

```bash
# Research existing patterns (use multiple tools in parallel)
Read src/client/DeckBuilding/domain/Deck/Deck.ts
Read src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeckRequest.ts
Grep "CardToDeck" src/client/DeckBuilding --glob "**/*.ts"

# Implement following TDD pattern
Write specs/unit/commands/newFeature.spec.ts  # Test first
Bash "npm test -- newFeature.spec.ts"         # See it fail
Write application/commands/newFeature/        # Implementation
Bash "npm test -- newFeature.spec.ts"         # See it pass
```

### Quality Assurance Workflow

```bash
# Before committing (run in parallel)
Bash "npm run lint"
Bash "npm test"

# If tests fail, debug systematically
Bash "npm test -- failing-test.spec.ts"
Read src/path/to/failing/file.ts
Edit src/path/to/failing/file.ts  # Fix issues
Bash "npm test -- failing-test.spec.ts"  # Verify fix
```

## Common Claude Code Workflows

### Adding a New Feature

1. **Identify Bounded Context**
   ```bash
   # Determine which context the feature belongs to
   LS src/client/
   ```

2. **Explore Existing Patterns**
   ```bash
   # Look at similar existing features
   Grep "similar-feature" src/client/[Context] --output_mode files_with_matches
   Read src/client/[Context]/application/commands/example/
   ```

3. **Follow TDD Approach**
   ```bash
   # Write test first
   Write specs/unit/commands/newFeature.spec.ts
   
   # Run test to see it fail
   Bash "npm test -- newFeature.spec.ts"
   
   # Implement feature
   Write application/commands/newFeature/
   
   # Run test to see it pass
   Bash "npm test -- newFeature.spec.ts"
   ```

4. **Quality Check**
   ```bash
   # Ensure code quality
   Bash "npm run lint"
   Bash "npm test"
   ```

### Debugging Issues

1. **Check Architecture Boundaries**
   ```bash
   # ESLint will catch boundary violations
   Bash "npm run lint"
   ```

2. **Verify Service Registration**
   ```bash
   # Check dependency injection setup
   Read src/server/Shared/DependencyInjection.ts
   ```

3. **Run Specific Tests**
   ```bash
   # Focus on failing area
   Bash "npm test -- path/to/failing/test"
   ```

### Working with 3D Gaming Components

**3D Components Location:**
```bash
# Gaming 3D components
LS src/client/Gaming/infrastructure/components/app/3d/
```

**Common 3D Patterns:**
- Camera management in `Camera/`
- Card rendering in `Card/`
- Game layouts in `Game*Layout/`
- Player areas in `Player**/`

## Project-Specific Claude Code Conventions

### Code Generation Best Practices

1. **Always Read Before Writing**
   ```bash
   # Before creating new files, understand existing patterns
   Read src/client/[Context]/application/commands/similar-feature/
   Read src/client/[Context]/specs/unit/commands/similar-feature.spec.ts
   ```

2. **Use Parallel Tool Calls**
   ```bash
   # Research multiple aspects simultaneously
   Read domain-file.ts
   Read application-service.ts  
   Read test-file.spec.ts
   Grep "pattern" src/context --glob "**/*.ts"
   ```

3. **Follow TDD Cycle Strictly**
   ```bash
   # Red -> Green -> Refactor
   Write test-file.spec.ts        # RED: Write failing test (only // Arrange, // Act, // Assert comments)
   Bash "npm test -- test-file"   # Confirm failure
   Write implementation.ts        # GREEN: Make it pass (NO COMMENTS, apply SOLID principles)
   Bash "npm test -- test-file"   # Confirm passing
   Edit implementation.ts         # REFACTOR: Extract until drop, improve clarity (NO COMMENTS)
   Bash "npm test"               # Ensure all tests pass
   ```

4. **Apply Code Quality Standards**
   ```bash
   # Ensure every line is justified by tests
   # Use full descriptive names without abbreviations
   # Focus on behavior over implementation
   # Apply SOLID principles throughout
   # Encapsulate external dependencies
   ```

### Architecture Compliance Workflow

1. **Respect Layer Boundaries**
   ```bash
   # Check imports follow dependency rule
   Read src/client/[Context]/domain/          # Should not import from application/infrastructure
   Read src/client/[Context]/application/     # Can import from domain, not infrastructure
   Read src/client/[Context]/infrastructure/  # Can import from application and domain
   ```

2. **Use Ports Pattern**
   ```bash
   # Always implement through interfaces
   Read src/client/[Context]/application/ports/ServiceName.ts    # Interface
   Read src/client/[Context]/infrastructure/services/ConvexServiceName.ts  # Implementation
   ```

### Testing Integration Workflow

1. **Component Testing**
   ```bash
   # Test React components through behavior
   Write specs/components/ComponentName.spec.tsx
   Bash "npm test -- ComponentName.spec.tsx"
   ```

2. **Integration Testing**
   ```bash
   # Test full user scenarios
   Write specs/integration/feature.spec.ts
   Bash "npm test -- feature.spec.ts"
   ```

### Convex Integration Patterns

1. **Server-Side Development**
   ```bash
   # Add new Convex mutation
   Write convex/mutations/newOperation.ts
   Write src/server/[Context]/infrastructure/repositories/ConvexRepository.ts
   Bash "npm run convex:dev"  # Test in development
   ```

2. **Client-Server Integration**
   ```bash
   # Connect client to Convex backend
   Read src/client/[Context]/infrastructure/services/ConvexService.ts
   Edit src/client/[Context]/infrastructure/services/ConvexService.ts
   Bash "npm run dev:all"  # Test full stack
   ```

### Performance Optimization

1. **Bundle Analysis**
   ```bash
   # Check bundle size and dependencies
   Bash "npm run build"
   Bash "npm run deps:all"  # Generate dependency graphs
   ```

2. **Test Performance**
   ```bash
   # Run with coverage to identify unused code
   Bash "npm run test:coverage"
   ```

### Git Integration Best Practices

1. **Pre-Commit Validation**
   ```bash
   # ALWAYS run before commits
   Bash "npm run lint"
   Bash "npm test"
   ```

2. **Branch Workflow**
   ```bash
   # Create descriptive branches in English
   Bash "git checkout -b feature/add-card-favorites"
   Bash "git checkout -b fix/deck-loading-error"
   Bash "git checkout -b refactor/improve-card-performance"
   ```

### Error Recovery Strategies

1. **Dependency Issues**
   ```bash
   # If npm install fails
   Bash "rm -rf node_modules package-lock.json"
   Bash "npm install"
   ```

2. **Test Failures**
   ```bash
   # Isolate and debug failing tests
   Bash "npm test -- --reporter=verbose failing-test.spec.ts"
   Read test-file.spec.ts
   Read implementation-file.ts
   ```

3. **Build Issues**
   ```bash
   # Check TypeScript and linting
   Bash "npx tsc --noEmit"  # Type checking only
   Bash "npm run lint:fix"  # Auto-fix linting issues
   ```

## Error Prevention

### Common Pitfalls to Avoid

1. **Import Violations**
   - Never import from implementations, use ports/interfaces
   - Always use `@/` alias for absolute imports

2. **Architecture Violations**  
   - Don't skip layers (domain → infrastructure directly)
   - Don't put business logic in React components

3. **Test Violations**
   - Don't test domain objects directly
   - Don't have multiple actions in one test
   - Don't forget `// Arrange`, `// Act`, `// Assert` comments

4. **Comment Violations**
   - **NEVER** add any comments except `// Arrange`, `// Act`, `// Assert` in tests
   - No explanatory comments, TODO comments, or documentation comments
   - Code must be self-documenting through clear naming

5. **Convex Integration**
   - Don't bypass the repository pattern
   - Don't put business logic in Convex functions

## File Templates

### Command Template
```typescript
// application/commands/[action]/[Action]Command.ts
export interface [Action]Command {
  readonly [property]: [Type];
}

// application/commands/[action]/[Action]CommandHandler.ts
export class [Action]CommandHandler {
  async execute(command: [Action]Command): Promise<void> {
    // Implementation
  }
}
```

### Query Template
```typescript
// application/queries/[action]/[action].ts
export const [action] = (state: RootState): [ReturnType] => {
  // Selector implementation
};
```

### Test Template
```typescript
// specs/unit/commands/[action].spec.ts
describe("When [context]", () => {
  it("should [expected behavior]", async () => {
    // Arrange
    const setup = createTestingEnvironment();
    
    // Act
    const result = await handler.execute(command);
    
    // Assert
    expect(result).toEqual(expected);
  });
});
```

## Resources

- **Architecture Documentation**: See AGENTS.md for detailed architecture guidance
- **Dependency Management**: Check package.json for available scripts
- **ESLint Rules**: Custom rules in eslint-plugin-custom-rules/
- **Convex Docs**: https://docs.convex.dev/
- **Testing**: Vitest with jest-extended matchers

---

*This configuration ensures Claude Code works effectively with the EvyGames codebase while respecting all architectural patterns and quality standards.*
- Always run npm run lint and npm run test at the end of your developpement. Both should always pass. No exception.
- Always use the convex mcp server to query the data of the database
- Always use sequential-thinking mcp server to track your tasks and advancement
- Always use sequential-thinking mcp server to track your tasks and advancement
