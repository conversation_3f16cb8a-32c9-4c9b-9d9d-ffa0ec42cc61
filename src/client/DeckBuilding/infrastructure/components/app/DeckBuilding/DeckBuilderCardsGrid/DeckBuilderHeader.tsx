'use client';

import {Badge, Flex, Heading, IconButton, SegmentedControl, Text, TextField} from '@radix-ui/themes';
import {MagnifyingGlassIcon, Pencil1Icon, PlusCircledIcon} from '@radix-ui/react-icons';
import {memo} from 'react';
import {ViewControlProps, SearchProps} from './DeckBuilderCardsGridComponent';

type DeckBuilderHeaderProps = {
  viewControl: ViewControlProps;
  search: SearchProps;
  onRenameClick: () => void;
  onAddTagClick: () => void;
};

const DeckBuilderHeader = memo(({
  viewControl,
  search,
  onRenameClick,
  onAddTagClick,
}: DeckBuilderHeaderProps) => {
  const {view, switchView, name, tags, cardCount} = viewControl;
  const {localSearch, setLocalSearch} = search;

  return (
    <Flex justify="between" align="center">
      {view === 'catalog' && (
        <Flex direction="column" pl="1">
          <Heading size="4">Search results</Heading>
          <Text as="span" size="2" color="gray">
            {cardCount}/{cardCount} cards found
          </Text>
        </Flex>
      )}
      {view === 'deck' && (
        <Flex direction="column" pl="1">
          <Flex align="center" gap="1">
            <Heading size="4">{name}</Heading>
            <IconButton
              ml="1"
              variant="surface"
              className="override-ghost"
              aria-label="Rename deck"
              size="1"
              onClick={onRenameClick}
            >
              <Pencil1Icon/>
            </IconButton>
          </Flex>
          <Flex gap="1">
            {tags.map(tag => (
              <Badge key={tag} size="1" color="grass">{tag}</Badge>
            ))}
            <IconButton
              variant="surface"
              className="override-ghost"
              aria-label="Add tag"
              size="1"
              onClick={onAddTagClick}
            >
              <PlusCircledIcon />
            </IconButton>
          </Flex>
        </Flex>
      )}

      <Flex align="center" gap="2">
        <SegmentedControl.Root
          value={view}
          onValueChange={value => switchView(value as 'catalog' | 'deck')}
          size="2"
          radius="medium"
        >
          <SegmentedControl.Item value="catalog">Catalog</SegmentedControl.Item>
          <SegmentedControl.Item value="deck">Deck</SegmentedControl.Item>
        </SegmentedControl.Root>

        <TextField.Root
          placeholder="Search for cards..."
          size="2"
          mr="4"
          type="search"
          style={{width: '300px'}}
          value={localSearch}
          onChange={e => setLocalSearch(e.target.value)}
        >
          <TextField.Slot>
            <MagnifyingGlassIcon height="16" width="16"/>
          </TextField.Slot>
        </TextField.Root>
      </Flex>
    </Flex>
  );
});

DeckBuilderHeader.displayName = 'DeckBuilderHeader';

export default DeckBuilderHeader;
