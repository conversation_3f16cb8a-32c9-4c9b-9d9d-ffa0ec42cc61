'use client';

import {Flex, ScrollArea, Text} from '@radix-ui/themes';
import DeckBuildingCard from '@/src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard';
import {memo} from 'react';
import {VirtualGridProps, CardOperationsProps} from './DeckBuilderCardsGridComponent';

type VirtualizedCardsGridProps = {
  virtualGrid: VirtualGridProps;
  cardOperations: CardOperationsProps;
};

const GAP = 8;

const VirtualizedCardsGrid = memo(({
  virtualGrid,
  cardOperations,
}: VirtualizedCardsGridProps) => {
  const {
    isLoading,
    cardRows,
    columnCount,
    containerRef,
    virtualRows,
    totalSize,
  } = virtualGrid;

  const {
    locale,
    quantities,
    addCardToDeck,
    removeCard,
    showCardDetails,
  } = cardOperations;

  if (isLoading) {
    return (
      <Flex direction="column" align="center" p="4" className="h-full">
        <Text size="4" color="gray" align="center" mt="4">
          Loading cards...
        </Text>
      </Flex>
    );
  }

  return (
    <ScrollArea
      type="always"
      scrollbars="vertical"
      className="h-full"
      ref={containerRef}
    >
      <div
        style={{
          height: totalSize,
          position: 'relative',
          width: '100%',
        }}
      >
        {virtualRows.map(virtualRow => {
          const cardsForRow = cardRows[virtualRow.index] ?? [];

          return (
            <div
              key={virtualRow.key}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                transform: `translateY(${virtualRow.start}px)`,
                display: 'grid',
                gridTemplateColumns: `repeat(${columnCount}, minmax(0, 1fr))`,
                gap: `${GAP}px`,
                width: '100%',
                paddingRight: '16px',
                alignItems: 'start',
              }}
            >
              {cardsForRow.map(card => (
                <div key={card.id} style={{width: '100%'}}>
                  <DeckBuildingCard
                    card={card}
                    locale={locale}
                    quantity={quantities.get(card.id) ?? card.minDeckQuantity}
                    addCardToDeck={addCardToDeck}
                    removeCard={removeCard}
                    showCardDetails={showCardDetails}
                  />
                </div>
              ))}
            </div>
          );
        })}
      </div>
    </ScrollArea>
  );
});

VirtualizedCardsGrid.displayName = 'VirtualizedCardsGrid';

export default VirtualizedCardsGrid;
