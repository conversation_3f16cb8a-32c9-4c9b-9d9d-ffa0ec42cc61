'use client';
import {FC, useState} from 'react';
import {Button, Dialog, Flex, Text, TextField} from '@radix-ui/themes';

type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddTag: (tag: string) => void;
};

const AddTagToDeckDialog: FC<Props> = ({open, onOpenChange, onAddTag}) => {
  const [tag, setTag] = useState('');

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Content style={{maxWidth: 450}}>
        <Dialog.Title>Add tag</Dialog.Title>
        <Dialog.Description size="2" mb="4">
          Choose a tag for your deck.
        </Dialog.Description>
        <Flex direction="column" gap="3">
          <label>
            <Text as="div" size="2" mb="1" weight="bold">
              Tag
            </Text>
            <TextField.Root
              value={tag}
              onChange={e => setTag(e.target.value)}
              placeholder="Enter tag"
            />
          </label>
        </Flex>
        <Flex gap="3" mt="4" justify="end">
          <Dialog.Close>
            <Button variant="soft" color="gray">
              Cancel
            </Button>
          </Dialog.Close>
          <Dialog.Close>
            <Button onClick={() => onAddTag(tag)}>Save</Button>
          </Dialog.Close>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
};

export default AddTagToDeckDialog;
