import {FC} from "react";
import {Callout, Text} from "@radix-ui/themes";
import {InfoCircledIcon} from "@radix-ui/react-icons";

const DeckBuilderDeckLoadingMessage: FC = () => {
  return (
    <Callout.Root color="iris" size="1">
      <Callout.Icon>
        <InfoCircledIcon/>
      </Callout.Icon>
      <Text size="2">
        Loading...
      </Text>
    </Callout.Root>
  );
};

export default DeckBuilderDeckLoadingMessage;
