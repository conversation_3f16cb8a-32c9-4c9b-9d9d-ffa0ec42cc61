import {render, waitFor} from '@testing-library/react';
import {Provider} from 'react-redux';
import {DeckBuilderInitializer} from '@/src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {FakeLocationService} from '@/src/client/Shared/services/Location/FakeLocationService';
import {getActiveFilters} from '@/src/client/DeckBuilding/application/queries/getActiveFilters/getActiveFilters';

describe('DeckBuilderInitializer', () => {
  describe('When filters are present in the location', () => {
    it('should apply those filters on load', async () => {
      // Arrange
      const locationService = new FakeLocationService({filters: ['INKABLE']});
      const store = createTestingStore({
        catalogFilters: {
          available: [
            {id: '1', name: 'INKABL<PERSON>', text: 'Inkable', dataProperty: 'inkable', dataType: 'boolean', value: true, order: 1},
            {id: '2', name: 'NOT_INKABLE', text: 'Not inkable', dataProperty: 'inkable', dataType: 'boolean', value: false, order: 2},
          ],
        },
      }, {locationService});

      // Act
      render(
        <Provider store={store}>
          <DeckBuilderInitializer />
        </Provider>
      );

      await waitFor(() => expect(getActiveFilters(store.getState())).toEqual(['INKABLE']));

      // Assert
      expect(locationService.getFilters()).toEqual(['INKABLE']);
    });
  });
});
