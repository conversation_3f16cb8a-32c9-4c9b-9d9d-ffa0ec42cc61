import {GameService, MatchData, LoadGameByIdResult} from '@/src/client/Gaming/application/ports/GameService';
import {api} from '@/convex/_generated/api';
import {convexClient} from '@/src/client/Shared/providers/ConvexClientProvider/ConvexClient';
import {Id} from '@/convex/_generated/dataModel';

export class ConvexGameService implements GameService {

  async loadMatchData(matchId: string): Promise<MatchData> {
    try {
      const result = await convexClient.query(api.queries.match.loadMatchData, {
        matchId: matchId as Id<'matches'>
      });

      if (result.error || !result.data) {
        throw new Error(result.error || 'Failed to load match data');
      }

      return {
        player1Cards: result.data.myCardIds,
        player2CardCount: result.data.opponentCardCount,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load match data';
      throw new Error(errorMessage);
    }
  }

  async loadGameById(gameId: string): Promise<LoadGameByIdResult> {
    try {
      const result = await convexClient.query(api.queries.loadGameById.endpoint, {
        gameId: gameId as Id<'games'>
      });

      if (result.error) {
        return { error: result.error };
      }

      return { data: result.data };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load game';
      return { error: errorMessage };
    }
  }
}
