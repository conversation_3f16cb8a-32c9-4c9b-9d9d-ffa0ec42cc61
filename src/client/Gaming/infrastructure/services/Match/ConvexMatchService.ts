import {MatchService, LeaveMatchResult, PlayCardResult} from '@/src/client/Gaming/application/ports/MatchService';
import {convexClient} from '@/src/client/Shared/providers/ConvexClientProvider/ConvexClient';
import {api} from '@/convex/_generated/api';
import {Id} from '@/convex/_generated/dataModel';

export class ConvexMatchService implements MatchService {
  async leaveMatch(matchId: string): Promise<LeaveMatchResult> {
    try {
      await convexClient.mutation(
        api.mutations.leaveMatch.endpoint,
        {matchId: matchId as Id<'matches'>}
      );
      return {};
    } catch (e) {
      const message = e instanceof Error ? e.message : 'Unknown error';
      return {error: message};
    }
  }

  async playCard(matchId: string, cardId: string, rowType: 'first' | 'second', slotIndex: number): Promise<PlayCardResult> {
    try {
      await convexClient.mutation(
        api.mutations.playCard.endpoint,
        {
          matchId: matchId as Id<'matches'>,
          cardId,
          rowType,
          slotIndex
        }
      );
      return {};
    } catch (e) {
      const message = e instanceof Error ? e.message : 'Unknown error';
      return {error: message};
    }
  }
};
