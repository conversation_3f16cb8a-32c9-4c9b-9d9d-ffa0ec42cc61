import {MatchService, LeaveMatchResult} from '@/src/client/Gaming/application/ports/MatchService';

export class FakeMatchService implements MatchService {
  private readonly result: LeaveMatchResult;
  private readonly shouldThrow: boolean;

  constructor(
    result: LeaveMatchResult = {},
    shouldThrow = false
  ) {
    this.result = result;
    this.shouldThrow = shouldThrow;
  }

  async leaveMatch(): Promise<LeaveMatchResult> {
    if (this.shouldThrow) {
      throw new Error('Failed to leave match');
    }
    return this.result;
  }
};
