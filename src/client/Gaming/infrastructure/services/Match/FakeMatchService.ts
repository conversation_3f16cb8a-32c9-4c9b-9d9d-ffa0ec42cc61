import {MatchService, LeaveMatchResult, PlayCardResult} from '@/src/client/Gaming/application/ports/MatchService';

export class FakeMatchService implements MatchService {
  private readonly leaveResult: LeaveMatchResult;
  private readonly playCardResult: PlayCardResult;
  private readonly shouldThrow: boolean;

  constructor(
    leaveResult: LeaveMatchResult = {},
    playCardResult: PlayCardResult = {},
    shouldThrow = false
  ) {
    this.leaveResult = leaveResult;
    this.playCardResult = playCardResult;
    this.shouldThrow = shouldThrow;
  }

  async leaveMatch(): Promise<LeaveMatchResult> {
    if (this.shouldThrow) {
      throw new Error('Failed to leave match');
    }
    return this.leaveResult;
  }

  async playCard(): Promise<PlayCardResult> {
    if (this.shouldThrow) {
      throw new Error('Failed to play card');
    }
    return this.playCardResult;
  }
};
