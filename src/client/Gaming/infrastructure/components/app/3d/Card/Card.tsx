'use client';

import {FC, useMemo} from 'react';
import {useLoader} from '@react-three/fiber';
import {Texture, TextureLoader} from 'three';
import {RoundedBoxGeometry} from 'three-stdlib';

type Props = {
  position: [number, number, number];
  rotation?: [number, number, number];
  scale?: number;
  id?: string;
  frontUrl?: string;
  visibleHeight?: number;
  width?: number;
  height?: number;
};

export const CARD_WIDTH = 2;
export const CARD_HEIGHT = 3;
const CARD_THICKNESS = 0.05;
const CARD_RADIUS = 0.15;
const SEGMENTS = 8;

const Card: FC<Props> = ({
                           position,
                           rotation = [0, 0, 0],
                           scale = 1.4,
                           id,
                           frontUrl,
                           visibleHeight,
                           width = CARD_WIDTH,
                           height = CARD_HEIGHT,
                         }) => {
  const [frontSource, backSource] = useLoader(
    TextureLoader,
    frontUrl
      ? [frontUrl, '/game-assets/cards/back.png']
      : id
        ? [`/game-assets/cards/en/thumbnail/${id}.jpg`, '/game-assets/cards/back.png']
        : ['/game-assets/cards/back-small.png', '/game-assets/cards/back-small.png'],
  ) as [Texture, Texture];

  const [front, back] = useMemo(
    () => [frontSource.clone(), backSource.clone()],
    [frontSource, backSource],
  );

  if (typeof visibleHeight === 'number' && front.image && visibleHeight < front.image.height) {
    const ratio = visibleHeight / front.image.height;
    front.repeat.set(1, ratio);
    front.offset.set(0, 1 - ratio);
    front.needsUpdate = true;
  }

  const geometry = useMemo(() => {
    return new RoundedBoxGeometry(width, CARD_THICKNESS, height, SEGMENTS, CARD_RADIUS);
  }, [width, height]);

  return (
    <group position={position} rotation={rotation} scale={scale}>
      {/*<CardEffects type="sparkles" color="yellow"/>*/}
      <mesh
        geometry={geometry}
        castShadow={true}
      >
        <meshStandardMaterial attach="material-0" color="#000000"/>
        <meshStandardMaterial attach="material-1" color="#000000"/>
        <meshStandardMaterial attach="material-2" map={front}/>
        <meshStandardMaterial attach="material-3" map={back}/>
        <meshStandardMaterial attach="material-4" color="#000000"/>
        <meshStandardMaterial attach="material-5" color="#000000"/>
      </mesh>
    </group>
  );
};

export default Card;
