'use client';

import {FC, useMemo} from 'react';
import {RoundedBoxGeometry} from 'three-stdlib';

type Props = {
  position: [number, number, number];
  rotation?: [number, number, number];
  scale?: number;
  width?: number;
  height?: number;
  onClick?: () => void;
};

const CARD_THICKNESS = 0.05;
const CARD_RADIUS = 0.15;
const SEGMENTS = 8;

const EmptySlot: FC<Props> = ({
  position,
  rotation = [0, 0, 0],
  scale = 1.4,
  width = 2,
  height = 2.1,
  onClick,
}) => {
  const geometry = useMemo(() => {
    return new RoundedBoxGeometry(width, CARD_THICKNESS, height, SEGMENTS, CARD_RADIUS);
  }, [width, height]);

  return (
    <group position={position} rotation={rotation} scale={scale}>
      <mesh
        geometry={geometry}
        castShadow={true}
        onClick={onClick}
      >
        <meshStandardMaterial attach="material-0" color="#333333" transparent opacity={0.3}/>
        <meshStandardMaterial attach="material-1" color="#333333" transparent opacity={0.3}/>
        <meshStandardMaterial attach="material-2" color="#444444" transparent opacity={0.5}/>
        <meshStandardMaterial attach="material-3" color="#444444" transparent opacity={0.5}/>
        <meshStandardMaterial attach="material-4" color="#333333" transparent opacity={0.3}/>
        <meshStandardMaterial attach="material-5" color="#333333" transparent opacity={0.3}/>
      </mesh>
    </group>
  );
};

export default EmptySlot;