'use client';

import {FC} from 'react';
import {useLoader} from '@react-three/fiber';
import {TextureLoader} from 'three';
import {useSelector} from 'react-redux';
import {getPlayer2HandCount} from '@/src/client/Gaming/application/queries/getPlayerHands/getPlayerHands';

type Props = Record<string, never>;

const RADIUS = 12.5;
const scale = 0.8;
const CARD_WIDTH = 2 * scale;
const CARD_HEIGHT = 3 * scale;

const Player2Hand: FC<Props> = () => {
  const cardCount = useSelector(getPlayer2HandCount);
  const imageUrls = Array.from({length: cardCount}, () => `/game-assets/cards/back.png`);
  const textures = useLoader(TextureLoader, imageUrls);
  const middleIndex = (cardCount - 1) / 2;

  const maxArc = Math.PI / 2.5;
  const minArc = Math.PI / 10;
  const ARC_WIDTH = (() => {
    if (cardCount === 2) return 0.15;
    if (cardCount === 3) return 0.25;
    return Math.min(maxArc, Math.max(minArc, (cardCount - 1) * 0.1));
  })();

  const angleStep = ARC_WIDTH / (cardCount - 1 || 1);

  return (
    <group position={[-2.5, 14.2, 0]}>
      {Array.from({length: cardCount}, (_, index) => index).map((index) => {
        const angle = (index - middleIndex) * angleStep;
        const x = Math.sin(angle) * RADIUS;
        const baseY = -Math.cos(angle) * RADIUS * 0.7;

        return (
          <group
            key={index}
            position={[x, baseY, -15 + index * 0.001]}
            rotation={[0, 0, angle + Math.PI]}
          >
            <mesh
              position={[0, -CARD_HEIGHT / 2, 0]}
            >
              <planeGeometry args={[CARD_WIDTH, CARD_HEIGHT]} />
              <meshBasicMaterial
                map={textures[index]}
                transparent={true}
                depthWrite={false}
                polygonOffset={true}
                polygonOffsetFactor={-1}
              />
            </mesh>
          </group>
        );
      })}
    </group>
  );
};

export default Player2Hand;
