import {FC} from "react";
import Player2SecondRow from "@/src/client/Gaming/infrastructure/components/app/3d/Player2Rows/Player2SecondRow";
import Player2FirstRow from "@/src/client/Gaming/infrastructure/components/app/3d/Player2Rows/Player2FirstRow";
import Player1FirstRow from "@/src/client/Gaming/infrastructure/components/app/3d/Player1Rows/Player1FirstRow";
import Player1SecondRow from "@/src/client/Gaming/infrastructure/components/app/3d/Player1Rows/Player1SecondRow";

type Props = {
  selectedCard?: string | null;
  onSlotClick?: (rowType: string, slotIndex: number) => void;
  currentPlayerPosition?: 'player1' | 'player2';
};

const Game2x7Layout: FC<Props> = ({selectedCard, onSlotClick, currentPlayerPosition}) => {
  // Render board zones based on current player perspective
  // Each player should see their own zones closest to them
  if (currentPlayerPosition === 'player2') {
    return (
      <>
        {/* Player 2's zones (closest to Player 2) */}
        <Player2FirstRow
          selectedCard={selectedCard}
          onSlotClick={(slotIndex) => onSlotClick?.('player2-first', slotIndex)}
          zPosition={0.4} // Closest to camera for Player 2
        />
        <Player2SecondRow
          selectedCard={selectedCard}
          onSlotClick={(slotIndex) => onSlotClick?.('player2-second', slotIndex)}
          zPosition={3.6} // Second closest for Player 2
        />
        {/* Player 1's zones (farther from Player 2) */}
        <Player1SecondRow zPosition={-3.2} />
        <Player1FirstRow zPosition={-6.4} />
      </>
    );
  }

  // Default layout for Player 1
  return (
    <>
      {/* Player 1's zones (closest to Player 1) */}
      <Player1FirstRow
        selectedCard={selectedCard}
        onSlotClick={(slotIndex) => onSlotClick?.('player1-first', slotIndex)}
        zPosition={0.4} // Closest to camera for Player 1
      />
      <Player1SecondRow
        selectedCard={selectedCard}
        onSlotClick={(slotIndex) => onSlotClick?.('player1-second', slotIndex)}
        zPosition={3.6} // Second closest for Player 1
      />
      {/* Player 2's zones (farther from Player 1) */}
      <Player2FirstRow zPosition={-3.2} />
      <Player2SecondRow zPosition={-6.4} />
    </>
  );
};

export default Game2x7Layout;