import {FC} from "react";
import Player2SecondRow from "@/src/client/Gaming/infrastructure/components/app/3d/Player2Rows/Player2SecondRow";
import Player2FirstRow from "@/src/client/Gaming/infrastructure/components/app/3d/Player2Rows/Player2FirstRow";
import Player1FirstRow from "@/src/client/Gaming/infrastructure/components/app/3d/Player1Rows/Player1FirstRow";
import Player1SecondRow from "@/src/client/Gaming/infrastructure/components/app/3d/Player1Rows/Player1SecondRow";

type Props = {
  selectedCard?: string | null;
  onSlotClick?: (rowType: string, slotIndex: number) => void;
  currentPlayerPosition?: 'player1' | 'player2';
};

const Game2x7Layout: FC<Props> = ({selectedCard, onSlotClick, currentPlayerPosition}) => {
  return (
    <>
      <Player2SecondRow
        selectedCard={currentPlayerPosition === 'player2' ? selectedCard : undefined}
        onSlotClick={currentPlayerPosition === 'player2' ? (slotIndex) => onSlotClick?.('player2-second', slotIndex) : undefined}
      />
      <Player2FirstRow
        selectedCard={currentPlayerPosition === 'player2' ? selectedCard : undefined}
        onSlotClick={currentPlayerPosition === 'player2' ? (slotIndex) => onSlotClick?.('player2-first', slotIndex) : undefined}
      />
      <Player1FirstRow 
        selectedCard={currentPlayerPosition === 'player1' ? selectedCard : undefined}
        onSlotClick={currentPlayerPosition === 'player1' ? (slotIndex) => onSlotClick?.('player1-first', slotIndex) : undefined}
      />
      <Player1SecondRow 
        selectedCard={currentPlayerPosition === 'player1' ? selectedCard : undefined}
        onSlotClick={currentPlayerPosition === 'player1' ? (slotIndex) => onSlotClick?.('player1-second', slotIndex) : undefined}
      />
    </>
  );
};

export default Game2x7Layout;