import {FC} from "react";
import Player2SecondRow from "@/src/client/Gaming/infrastructure/components/app/3d/Player2Rows/Player2SecondRow";
import Player2FirstRow from "@/src/client/Gaming/infrastructure/components/app/3d/Player2Rows/Player2FirstRow";
import Player1FirstRow from "@/src/client/Gaming/infrastructure/components/app/3d/Player1Rows/Player1FirstRow";
import Player1SecondRow from "@/src/client/Gaming/infrastructure/components/app/3d/Player1Rows/Player1SecondRow";

type Props = {
  selectedCard?: string | null;
  onSlotClick?: (rowType: string, slotIndex: number) => void;
};

const Game2x7Layout: FC<Props> = ({selectedCard, onSlotClick}) => {
  return (
    <>
      <Player2SecondRow/>
      <Player2FirstRow/>
      <Player1FirstRow 
        selectedCard={selectedCard}
        onSlotClick={(slotIndex) => onSlotClick?.('player1-first', slotIndex)}
      />
      <Player1SecondRow 
        selectedCard={selectedCard}
        onSlotClick={(slotIndex) => onSlotClick?.('player1-second', slotIndex)}
      />
    </>
  );
};

export default Game2x7Layout;