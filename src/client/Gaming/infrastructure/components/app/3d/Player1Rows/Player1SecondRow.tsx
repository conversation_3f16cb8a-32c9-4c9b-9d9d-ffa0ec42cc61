'use client';

import {FC, useState, useMemo} from 'react';
import {useLoader} from '@react-three/fiber';
import {TextureLoader} from 'three';
import PreloadedCard from '@/src/client/Gaming/infrastructure/components/app/3d/PreloadedCard/PreloadedCard';
import EmptySlot from '@/src/client/Gaming/infrastructure/components/app/3d/EmptySlot/EmptySlot';
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';
import {showZoomedCard} from '@/src/client/Gaming/application/commands/showZoomedCard/showZoomedCard';
import {getPlayer1Hand} from '@/src/client/Gaming/application/queries/getPlayerHands/getPlayerHands';

const CARD_Y = -1.380;
const CARD_Z = 3.6;
const CARD_SPACING = 3;
const SLOT_COUNT = 8;

type Props = {
  position?: [number, number, number];
  selectedCard?: string | null;
  onSlotClick?: (slotIndex: number) => void;
};

const HOVER_OFFSET = 0.05;

const Player1SecondRow: FC<Props> = ({position = [0, 0, 0], selectedCard, onSlotClick}) => {
  const dispatch = useDispatch<AppDispatch>();
  const cardIds = useSelector(getPlayer1Hand);
  const offset = ((SLOT_COUNT - 1) * CARD_SPACING) / 2;
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [placedCards, setPlacedCards] = useState<(string | null)[]>(Array(SLOT_COUNT).fill(null));
  
  const preloadedTextures = useLoader(TextureLoader, cardIds);
  const backTexture = useLoader(TextureLoader, '/game-assets/cards/back.png');
  const textureMap = useMemo(() => {
    const map = new Map();
    cardIds.forEach((cardId, index) => {
      map.set(cardId, preloadedTextures[index]);
    });
    return map;
  }, [cardIds, preloadedTextures]);

  const handleSlotClick = (slotIndex: number) => {
    if (selectedCard && placedCards[slotIndex] === null) {
      const newPlacedCards = [...placedCards];
      newPlacedCards[slotIndex] = selectedCard;
      setPlacedCards(newPlacedCards);
      onSlotClick?.(slotIndex);
    }
  };

  return (
    <group position={position}>
      {Array.from({length: SLOT_COUNT}, (_, index) => (
        <group
          key={index}
          onPointerOver={(e) => {
            e.stopPropagation();
            setHoveredIndex(index);
            document.body.style.cursor = 'pointer';
          }}
          onPointerOut={(e) => {
            e.stopPropagation();
            setHoveredIndex(null);
            document.body.style.cursor = 'default';
          }}
          onClick={(e) => {
            e.stopPropagation();
            handleSlotClick(index);
          }}
          onContextMenu={(e) => {
            e.stopPropagation();
            if (placedCards[index]) {
              dispatch(showZoomedCard({cardId: placedCards[index]!}));
            }
          }}
          onPointerDown={(e) => {
            e.stopPropagation();
            if (e.button === 2 && placedCards[index]) {
              dispatch(showZoomedCard({cardId: placedCards[index]!}));
            }
          }}
        >
          {placedCards[index] ? (
            <PreloadedCard
              key={`placed-${placedCards[index]}`}
              frontTexture={textureMap.get(placedCards[index]!)!}
              backTexture={backTexture}
              width={2}
              height={2.1}
              position={[
                index * CARD_SPACING - offset,
                CARD_Y + (hoveredIndex === index ? HOVER_OFFSET : 0),
                CARD_Z,
              ]}
              visibleHeight={340}
            />
          ) : (
            <EmptySlot
              key={`empty-${index}`}
              width={2}
              height={2.1}
              position={[
                index * CARD_SPACING - offset,
                CARD_Y + (hoveredIndex === index ? HOVER_OFFSET : 0),
                CARD_Z,
              ]}
              onClick={() => handleSlotClick(index)}
            />
          )}
        </group>
      ))}
    </group>
  );
};

export default Player1SecondRow;
