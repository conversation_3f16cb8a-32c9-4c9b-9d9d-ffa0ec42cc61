'use client';

import {FC} from 'react';
import {Image} from '@react-three/drei';
import {useLoader} from '@react-three/fiber';
import {TextureLoader} from 'three';

const Table: FC = () => {
  const texture = useLoader(TextureLoader, '/game-assets/table.png');
  const ratio = texture.image.width / texture.image.height;

  return (
    // eslint-disable-next-line jsx-a11y/alt-text
    <Image
      url="/game-assets/table.png"
      scale={[44, 44 / ratio]}
      position={[0, -1.5, 0]}
      rotation={[-Math.PI / 2, 0, 0]}
    />
  );
};

export default Table;
