'use client';

import {FC, useMemo} from 'react';
import {Texture} from 'three';
import {RoundedBoxGeometry} from 'three-stdlib';

type Props = {
  position: [number, number, number];
  rotation?: [number, number, number];
  scale?: number;
  frontTexture: Texture;
  backTexture?: Texture;
  visibleHeight?: number;
  width?: number;
  height?: number;
};

export const CARD_WIDTH = 2;
export const CARD_HEIGHT = 3;
const CARD_THICKNESS = 0.05;
const CARD_RADIUS = 0.15;
const SEGMENTS = 8;

const PreloadedCard: FC<Props> = ({
  position,
  rotation = [0, 0, 0],
  scale = 1.4,
  frontTexture,
  backTexture,
  visibleHeight,
  width = CARD_WIDTH,
  height = CARD_HEIGHT,
}) => {
  const [front, back] = useMemo(() => {
    const frontClone = frontTexture.clone();
    const backClone = backTexture ? backTexture.clone() : frontTexture.clone();
    
    if (typeof visibleHeight === 'number' && frontClone.image && visibleHeight < frontClone.image.height) {
      const ratio = visibleHeight / frontClone.image.height;
      frontClone.repeat.set(1, ratio);
      frontClone.offset.set(0, 1 - ratio);
      frontClone.needsUpdate = true;
    }
    
    return [frontClone, backClone];
  }, [frontTexture, backTexture, visibleHeight]);

  const geometry = useMemo(() => {
    return new RoundedBoxGeometry(width, CARD_THICKNESS, height, SEGMENTS, CARD_RADIUS);
  }, [width, height]);

  return (
    <group position={position} rotation={rotation} scale={scale}>
      <mesh
        geometry={geometry}
        castShadow={true}
      >
        <meshStandardMaterial attach="material-0" color="#000000"/>
        <meshStandardMaterial attach="material-1" color="#000000"/>
        <meshStandardMaterial attach="material-2" map={front}/>
        <meshStandardMaterial attach="material-3" map={back}/>
        <meshStandardMaterial attach="material-4" color="#000000"/>
        <meshStandardMaterial attach="material-5" color="#000000"/>
      </mesh>
    </group>
  );
};

export default PreloadedCard;