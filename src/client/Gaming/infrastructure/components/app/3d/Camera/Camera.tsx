'use client';

import {FC, useEffect} from "react";
import {OrbitControls, PerspectiveCamera} from "@react-three/drei";
import {useThree} from "@react-three/fiber";

const Camera: FC = () => {
  const { camera } = useThree();

  useEffect(() => {
    camera.layers.enable(1);
  }, [camera]);

  return (
    <>
      <PerspectiveCamera makeDefault fov={20} position={[0, 45, 25]} />
      <OrbitControls enabled={false} target={[0, 0, 0]} />
    </>
  );
};

export default Camera;
