'use client';

import {FC, useState} from 'react';
import PreloadedCard from '@/src/client/Gaming/infrastructure/components/app/3d/PreloadedCard/PreloadedCard';
import EmptySlot from '@/src/client/Gaming/infrastructure/components/app/3d/EmptySlot/EmptySlot';
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';
import {showZoomedCard} from '@/src/client/Gaming/application/commands/showZoomedCard/showZoomedCard';
import {getPlayer2FirstRow} from '@/src/client/Gaming/application/queries/getGameBoard/getGameBoard';
import {useTextureCache} from '@/src/client/Gaming/infrastructure/components/app/3d/TextureCache/TextureCache';

const CARD_Y = -1.380;
const CARD_Z = -3.2;
const CARD_ROTATION: [number, number, number] = [0, 0, 0];
const CARD_SPACING = 3;
const SLOT_COUNT = 8;

type Props = {
  position?: [number, number, number];
  selectedCard?: string | null;
  onSlotClick?: (slotIndex: number) => void;
  zPosition?: number;
};

const HOVER_OFFSET = 0.1;

const Player2FirstRow: FC<Props> =  ({position = [0, 0, 0], selectedCard, onSlotClick, zPosition}) => {
  const dispatch = useDispatch<AppDispatch>();
  const boardRow = useSelector(getPlayer2FirstRow);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const offset = ((SLOT_COUNT - 1) * CARD_SPACING) / 2;
  const cardZ = zPosition ?? CARD_Z;
  const {getTexture, backTexture} = useTextureCache();

  const handleSlotClick = (slotIndex: number) => {
    if (selectedCard && boardRow[slotIndex] === null) {
      onSlotClick?.(slotIndex);
    }
  };

  return (
    <group position={position}>
      {Array.from({length: SLOT_COUNT}, (_, index) => (
        <group
          key={index}
          onPointerOver={(e) => {
            e.stopPropagation();
            setHoveredIndex(index);
            document.body.style.cursor = 'pointer';
          }}
          onPointerOut={(e) => {
            e.stopPropagation();
            setHoveredIndex(null);
            document.body.style.cursor = 'default';
          }}
          onContextMenu={(e) => {
            e.stopPropagation();
            if (boardRow[index]) {
              dispatch(showZoomedCard({cardId: boardRow[index]!.cardId}));
            }
          }}
          onClick={(e) => {
            e.stopPropagation();
            handleSlotClick(index);
          }}
          onPointerDown={(e) => {
            e.stopPropagation();
            if (e.button === 2 && boardRow[index]) {
              dispatch(showZoomedCard({cardId: boardRow[index]!.cardId}));
            }
          }}
        >
          {boardRow[index] ? (
            <PreloadedCard
              key={`placed-${boardRow[index]!.cardId}`}
              frontTexture={getTexture(boardRow[index]!.cardId) || backTexture}
              backTexture={backTexture}
              width={2}
              height={2.1}
              position={[
                index * CARD_SPACING - offset,
                CARD_Y + (hoveredIndex === index ? HOVER_OFFSET : 0),
                cardZ,
              ]}
              rotation={CARD_ROTATION}
              visibleHeight={340}
            />
          ) : (
            <EmptySlot
              key={`empty-${index}`}
              width={2}
              height={2.1}
              position={[
                index * CARD_SPACING - offset,
                CARD_Y + (hoveredIndex === index ? HOVER_OFFSET : 0),
                cardZ,
              ]}
              rotation={CARD_ROTATION}
            />
          )}
        </group>
      ))}
    </group>
  );
};

export default Player2FirstRow;
