'use client';

import {FC, useState, useMemo} from 'react';
import {useLoader} from '@react-three/fiber';
import {TextureLoader} from 'three';
import PreloadedCard from '@/src/client/Gaming/infrastructure/components/app/3d/PreloadedCard/PreloadedCard';
import EmptySlot from '@/src/client/Gaming/infrastructure/components/app/3d/EmptySlot/EmptySlot';
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';
import {showZoomedCard} from '@/src/client/Gaming/application/commands/showZoomedCard/showZoomedCard';
import {getPlayer2FirstRow} from '@/src/client/Gaming/application/queries/getGameBoard/getGameBoard';

const CARD_Y = -1.380;
const CARD_Z = -3.2;
const CARD_ROTATION: [number, number, number] = [0, 0, 0];
const CARD_SPACING = 3;
const SLOT_COUNT = 8;

type Props = {
  position?: [number, number, number];
  selectedCard?: string | null;
  onSlotClick?: (slotIndex: number) => void;
};

const HOVER_OFFSET = 0.1;

const Player2FirstRow: FC<Props> =  ({position = [0, 0, 0], selectedCard, onSlotClick}) => {
  const dispatch = useDispatch<AppDispatch>();
  const boardRow = useSelector(getPlayer2FirstRow);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const offset = ((SLOT_COUNT - 1) * CARD_SPACING) / 2;
  
  const backTexture = useLoader(TextureLoader, '/game-assets/cards/back.png');
  const boardCardIds = useMemo(() => {
    return boardRow.filter(card => card !== null).map(card => card!.cardId);
  }, [boardRow]);

  const allTextures = useLoader(TextureLoader, boardCardIds.length > 0 ? boardCardIds : ['/game-assets/cards/back-small.png']);

  const textureMap = useMemo(() => {
    const map = new Map();
    boardCardIds.forEach((cardId, index) => {
      map.set(cardId, Array.isArray(allTextures) ? allTextures[index] : allTextures);
    });
    return map;
  }, [boardCardIds, allTextures]);

  const handleSlotClick = (slotIndex: number) => {
    if (selectedCard && boardRow[slotIndex] === null) {
      onSlotClick?.(slotIndex);
    }
  };

  return (
    <group position={position}>
      {Array.from({length: SLOT_COUNT}, (_, index) => (
        <group
          key={index}
          onPointerOver={(e) => {
            e.stopPropagation();
            setHoveredIndex(index);
            document.body.style.cursor = 'pointer';
          }}
          onPointerOut={(e) => {
            e.stopPropagation();
            setHoveredIndex(null);
            document.body.style.cursor = 'default';
          }}
          onContextMenu={(e) => {
            e.stopPropagation();
            if (boardRow[index]) {
              dispatch(showZoomedCard({cardId: boardRow[index]!.cardId}));
            }
          }}
          onClick={(e) => {
            e.stopPropagation();
            handleSlotClick(index);
          }}
          onPointerDown={(e) => {
            e.stopPropagation();
            if (e.button === 2 && boardRow[index]) {
              dispatch(showZoomedCard({cardId: boardRow[index]!.cardId}));
            }
          }}
        >
          {boardRow[index] ? (
            <PreloadedCard
              key={`placed-${boardRow[index]!.cardId}`}
              frontTexture={textureMap.get(boardRow[index]!.cardId) || textureMap.values().next().value}
              backTexture={backTexture}
              width={2}
              height={2.1}
              position={[
                index * CARD_SPACING - offset,
                CARD_Y + (hoveredIndex === index ? HOVER_OFFSET : 0),
                CARD_Z,
              ]}
              rotation={CARD_ROTATION}
              visibleHeight={340}
            />
          ) : (
            <EmptySlot
              key={`empty-${index}`}
              width={2}
              height={2.1}
              position={[
                index * CARD_SPACING - offset,
                CARD_Y + (hoveredIndex === index ? HOVER_OFFSET : 0),
                CARD_Z,
              ]}
              rotation={CARD_ROTATION}
            />
          )}
        </group>
      ))}
    </group>
  );
};

export default Player2FirstRow;
