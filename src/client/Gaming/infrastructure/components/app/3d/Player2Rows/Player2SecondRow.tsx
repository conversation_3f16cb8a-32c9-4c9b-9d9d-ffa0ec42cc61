'use client';

import {FC, useState} from 'react';
import EmptySlot from '@/src/client/Gaming/infrastructure/components/app/3d/EmptySlot/EmptySlot';

const CARD_Y = -1.380;
const CARD_Z = -6.4;
const CARD_ROTATION: [number, number, number] = [0, 0, 0];
const CARD_SPACING = 3;
const SLOT_COUNT = 8;

const HOVER_OFFSET = 0.1;

type Props = {
  position?: [number, number, number];
};

const Player2SecondRow: FC<Props> = ({position = [0, 0, 0]}) => {
  const offset = ((SLOT_COUNT - 1) * CARD_SPACING) / 2;
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  return (
    <group position={position}>
      {Array.from({length: SLOT_COUNT}, (_, index) => (
        <group
          key={index}
          onPointerOver={(e) => {
            e.stopPropagation();
            setHoveredIndex(index);
            document.body.style.cursor = 'pointer';
          }}
          onPointerOut={(e) => {
            e.stopPropagation();
            setHoveredIndex(null);
            document.body.style.cursor = 'default';
          }}
        >
          <EmptySlot
            width={2}
            height={2.1}
            position={[
              index * CARD_SPACING - offset,
              CARD_Y + (hoveredIndex === index ? HOVER_OFFSET : 0),
              CARD_Z,
            ]}
            rotation={CARD_ROTATION}
          />
        </group>
      ))}
    </group>
  );
};

export default Player2SecondRow;
