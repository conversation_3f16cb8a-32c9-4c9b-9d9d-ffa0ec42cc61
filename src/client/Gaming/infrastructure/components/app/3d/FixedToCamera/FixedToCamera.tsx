'use client';

import {useThree} from '@react-three/fiber';
import {useRef, useEffect, type ReactNode} from 'react';
import {Group} from 'three';

type Props = {
  position?: [number, number, number];
  children: ReactNode;
};

const FixedToCamera = ({position = [2.5, -2, -15], children}: Props) => {
  const {camera, scene} = useThree();
  const groupRef = useRef<Group>(null);

  useEffect(() => {
    const group = groupRef.current;
    if (!group) return;

    camera.add(group);
    scene.add(camera);

    return () => {
      camera.remove(group);
    };
  }, [camera, scene]);

  return (
    <group ref={groupRef} position={position}>
      {children}
    </group>
  );
};

export default FixedToCamera;
