'use client';

import {FC, useEffect, useState} from "react";
import Camera from "@/src/client/Gaming/infrastructure/components/app/3d/Camera/Camera";
import Lights from "@/src/client/Gaming/infrastructure/components/app/3d/Lights/Lights";
import Table from "@/src/client/Gaming/infrastructure/components/app/3d/Table/Table";
import Shadow from "@/src/client/Gaming/infrastructure/components/app/3d/Shadow/Shadow";
import FixedToCamera from "@/src/client/Gaming/infrastructure/components/app/3d/FixedToCamera/FixedToCamera";
import Player1Hand from "@/src/client/Gaming/infrastructure/components/app/3d/Player1Hand/Player1Hand";
import Player2Hand from "@/src/client/Gaming/infrastructure/components/app/3d/Player2Hand/Player2Hand";
import {useDispatch} from 'react-redux';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';
import {initializeGameScene} from '@/src/client/Gaming/application/commands/initializeGameScene/initializeGameScene';
import Game2x7Layout from "@/src/client/Gaming/infrastructure/components/app/3d/Game2x7Layout/Game2x7Layout";

type Props = {
  matchId?: string;
};

const GameScene: FC<Props> = ({matchId}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [selectedCard, setSelectedCard] = useState<string | null>(null);

  useEffect(() => {
    dispatch(initializeGameScene({matchId}));
  }, [dispatch, matchId]);

  const handleCardSelect = (cardId: string) => {
    setSelectedCard(selectedCard === cardId ? null : cardId);
  };

  const handleSlotClick = (rowType: string, slotIndex: number) => {
    console.log(`Card ${selectedCard} placed in ${rowType} slot ${slotIndex}`);
    setSelectedCard(null);
  };

  return (
    <>
      <Camera/>
      <Lights/>
      <Table/>
      <Shadow/>
      <Game2x7Layout 
        selectedCard={selectedCard}
        onSlotClick={handleSlotClick}
      />
      <FixedToCamera>
        <Player2Hand/>
        <Player1Hand 
          onCardSelect={handleCardSelect}
        />
      </FixedToCamera>
    </>
  );
};

export default GameScene;
