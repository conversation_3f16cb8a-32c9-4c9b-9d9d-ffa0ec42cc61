'use client';

import {FC, useState, useMemo} from "react";
import {Flex} from "@radix-ui/themes";
import GameListContent from "@/src/client/Gaming/infrastructure/components/app/Gaming/GameListContent/GameListContent";
import GameSearchBar from "@/src/client/Gaming/infrastructure/components/app/Gaming/GameSearchBar/GameSearchBar";

type Game = {
  id: string;
  name: string;
  description?: string;
  playerCount?: string;
  imageUrl?: string;
};

type Props = {
  games: Game[];
};

const GameListWithSearch: FC<Props> = ({games}) => {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredGames = useMemo(() => {
    if (!searchQuery.trim()) {
      return games;
    }
    
    const query = searchQuery.toLowerCase().trim();
    return games.filter(game =>
      game.name.toLowerCase().includes(query) ||
      (game.description && game.description.toLowerCase().includes(query))
    );
  }, [games, searchQuery]);

  return (
    <Flex direction="column" gap="4">
      <Flex justify="center">
        <GameSearchBar 
          value={searchQuery}
          onChange={setSearchQuery}
          placeholder="Search games by name or description..."
        />
      </Flex>
      
      <GameListContent games={filteredGames} />
    </Flex>
  );
};

export default GameListWithSearch;