import {FC} from "react";
import MatchConsoleEvents
  from "@/src/client/Gaming/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents";
import {Id} from "@/convex/_generated/dataModel";
import {
  LeaveMatchButton
} from "@/src/client/Gaming/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton";
import {
  CardZoom
} from "@/src/client/Gaming/infrastructure/components/app/Gaming/CardZoom/CardZoom";

type Props = {
  matchId: string;
};

const HUD: FC<Props> = ({matchId}) => {
  return (
    <>
      <div style={{
        position: 'absolute',
        bottom: '20px',
        right: '20px',
        zIndex: 10,
        maxWidth: '400px',
        maxHeight: '300px',
        overflow: 'auto'
      }}>
        <MatchConsoleEvents matchId={matchId as Id<"matches">}/>
      </div>

      <div style={{
        position: 'absolute',
        top: '20px',
        left: '20px',
        zIndex: 10
      }}>
        <LeaveMatchButton matchId={matchId as Id<"matches">}/>
      </div>

      <CardZoom />
    </>
  );
};

export default HUD;