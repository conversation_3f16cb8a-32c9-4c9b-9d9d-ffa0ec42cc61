'use client';

import {Button, Flex, Text} from "@radix-ui/themes";
import {useCallback} from "react";
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';
import {leaveMatch} from '@/src/client/Gaming/application/commands/leaveMatch/leaveMatch';
import {getMatchError} from '@/src/client/Gaming/application/queries/getMatchError/getMatchError';
import ConfirmGameAlert from "@/src/client/Gaming/infrastructure/components/app/Gaming/ConfirmGameAlert/ConfirmGameAlert";

type Props = {
  matchId: string;
};

export const LeaveMatchButton = ({matchId}: Props) => {
  const dispatch = useDispatch<AppDispatch>();
  const error = useSelector(getMatchError);

  const handleLeaveMatch = useCallback(async () => {
    try {
      await dispatch(leaveMatch({matchId})).unwrap();
    } catch (err) {
      console.error('Failed to leave match:', err);
    }
  }, [dispatch, matchId]);

  return (
    <Flex direction="column" gap="3">
      {error && <Text color="red">{error}</Text>}
      <ConfirmGameAlert
        title="Give up"
        text="Are you sure you want to leave the match?"
        onAccept={handleLeaveMatch}
      >
        <Button
          size="3"
          color="red"
          variant="surface"
        >
          Leave Match
        </Button>
      </ConfirmGameAlert>
    </Flex>
  );
};
