'use client';

import {FC} from "react";
import {Card, Flex, Heading, Text} from "@radix-ui/themes";
import {Sparkles} from "@/src/client/Shared/components/Sparkles/Sparkles";
import {useTranslations} from "next-intl";

const GameListEmptyState: FC = () => {
  const t = useTranslations('games.emptyState');

  return (
    <Card size="4" className="text-center p-8">
      <Flex direction="column" align="center" gap="4">
        <div className="relative">
          <Sparkles />
          <div className="text-6xl opacity-20">🎮</div>
        </div>
        <Heading size="6" className="mb-2">
          {t('title')}
        </Heading>
        <Text size="3" color="gray" className="max-w-md">
          {t('description')}
        </Text>
      </Flex>
    </Card>
  );
};

export default GameListEmptyState;