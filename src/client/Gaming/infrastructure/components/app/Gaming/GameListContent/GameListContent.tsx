'use client';

import {FC} from "react";
import {Grid} from "@radix-ui/themes";
import GameCard from "@/src/client/Gaming/infrastructure/components/app/Gaming/GameCard/GameCard";
import GameListEmptyState from "@/src/client/Gaming/infrastructure/components/app/Gaming/GameListEmptyState/GameListEmptyState";

type Game = {
  id: string;
  name: string;
  description?: string;
  playerCount?: string;
  imageUrl?: string;
};

type Props = {
  games: Game[];
};

const GameListContent: FC<Props> = ({games}) => {
  if (games.length === 0) {
    return <GameListEmptyState />;
  }

  return (
    <Grid 
      columns={{
        initial: "1",
        xs: "2", 
        sm: "3",
        md: "4",
        lg: "5",
      }}
      gap="4"
    >
      {games.map((game) => (
        <GameCard
          key={game.id}
          gameId={game.id}
          name={game.name}
          description={game.description}
          playerCount={game.playerCount}
          imageUrl={game.imageUrl}
        />
      ))}
    </Grid>
  );
};

export default GameListContent;