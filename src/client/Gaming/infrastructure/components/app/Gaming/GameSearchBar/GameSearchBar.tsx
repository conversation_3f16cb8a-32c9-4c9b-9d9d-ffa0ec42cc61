'use client';

import {FC} from "react";
import {TextField, Flex} from "@radix-ui/themes";
import {useTranslations} from "next-intl";
import {Search} from "lucide-react";

type Props = {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
};

const GameSearchBar: FC<Props> = ({value, onChange, placeholder}) => {
  const t = useTranslations('games.search');
  const defaultPlaceholder = placeholder || t('placeholder');
  return (
    <Flex className="w-full max-w-md">
      <TextField.Root
        size="3"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={defaultPlaceholder}
        className="w-full"
      >
        <TextField.Slot>
          <Search/>
        </TextField.Slot>
      </TextField.Root>
    </Flex>
  );
};

export default GameSearchBar;