'use client';

import {FC} from 'react';
import Image from 'next/image';

type Props = {
  cardId: string;
  imageUrl: string;
  isSelected: boolean;
  onToggle: (cardId: string) => void;
};

const MulliganCard: FC<Props> = ({cardId, imageUrl, isSelected, onToggle}) => {
  return (
    <div 
      className={`relative w-48 h-64 cursor-pointer transition-all ${
        isSelected ? 'ring-2 ring-red-500 transform scale-105' : 'hover:scale-102'
      }`}
      onClick={() => onToggle(cardId)}
    >
          <Image
            src={imageUrl}
            alt={`Card ${cardId}`}
            fill
            className="object-cover rounded-lg"
            sizes="100vw"
          />
          {isSelected && (
            <div className="absolute inset-0 bg-red-500 bg-opacity-30 rounded-lg flex items-center justify-center pointer-events-none">
              <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">X</span>
              </div>
            </div>
          )}
    </div>
  );
};

export default MulliganCard;