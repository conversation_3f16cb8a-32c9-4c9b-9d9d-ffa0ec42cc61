'use client';

import {FC} from "react";
import {Grid, Skeleton} from "@radix-ui/themes";
import SkeletonHelper from "@/src/client/Shared/components/SkeletonHelper/SkeletonHelper";

const GameCardSkeleton: FC = () => (
  <Skeleton className="h-[60px] rounded-md" />
);

const GameListSkeleton: FC = () => {
  return (
    <Grid columns="6" gap="3">
      <SkeletonHelper amount={6} component={GameCardSkeleton} />
    </Grid>
  );
};

export default GameListSkeleton;