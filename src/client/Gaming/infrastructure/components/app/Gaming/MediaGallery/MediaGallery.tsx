'use client';

import { Grid, Flex, Text } from '@radix-ui/themes';
import Image from 'next/image';
import { FC } from 'react';
import ShiningCard from '@/src/client/Shared/components/ShiningCard/ShiningCard';

interface MediaItem {
  src: string;
  alt: string;
  type: 'image' | 'video';
}

interface MediaGalleryProps {
  items: MediaItem[];
  emptyMessage: string;
  className?: string;
}

const MediaGallery: FC<MediaGalleryProps> = ({ items, emptyMessage, className = '' }) => {
  if (!items || items.length === 0) {
    return (
      <Flex justify="center" align="center" className="min-h-32">
        <Text size="3" color="gray">{emptyMessage}</Text>
      </Flex>
    );
  }

  return (
    <div className={`overflow-hidden ${className}`}>
      <Grid 
        columns={{ initial: '1', sm: '2', md: '3', lg: '4' }}
        gap="4"
        className="p-4"
      >
        {items.map((item, index) => (
          <ShiningCard
            key={index}
            disableScaling
            className="group relative aspect-video overflow-hidden rounded-xl border border-gray-6 bg-gray-2 shadow-sm transition-all duration-300 hover:border-gray-7 hover:shadow-lg"
          >
            <Image
              src={item.src}
              alt={item.alt}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
            />
            {item.type === 'video' && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-white/90 shadow-lg">
                  <div className="ml-1 h-0 w-0 border-l-4 border-y-2 border-l-gray-900 border-y-transparent" />
                </div>
              </div>
            )}
          </ShiningCard>
        ))}
      </Grid>
    </div>
  );
};

export default MediaGallery;