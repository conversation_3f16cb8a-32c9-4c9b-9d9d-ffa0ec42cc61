'use client'

import {FC, useEffect, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import {useRouter} from "next/navigation";
import {Container, Flex, Text} from "@radix-ui/themes";
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";
import {getPlayerQueueStatus} from "@/src/client/Gaming/application/queries/getPlayerQueueStatus/getPlayerQueueStatus";
import {getCurrentQueueStatus} from "@/src/client/Gaming/application/queries/getCurrentQueueStatus/getCurrentQueueStatus";
import {buildWaitingForOpponentUrl} from "@/src/client/Shared/helpers/UrlBuilder/urlBuilder";
import PlayGamePage from "@/src/client/Gaming/infrastructure/pages/Gaming/PlayGamePage/PlayGamePage";

type Props = {
  gameId: string;
  locale: string;
};

const PlayGamePageWithQueueCheck: FC<Props> = ({gameId, locale}) => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const queueStatus = useSelector(getCurrentQueueStatus);
  const [isCheckingQueue, setIsCheckingQueue] = useState(true);

  useEffect(() => {
    const checkQueue = async () => {
      try {
        await dispatch(getPlayerQueueStatus({gameId})).unwrap();
      } catch (error) {
        console.error('Error checking queue status:', error);
      } finally {
        setIsCheckingQueue(false);
      }
    };

    checkQueue();
  }, [dispatch, gameId]);

  useEffect(() => {
    if (!isCheckingQueue && queueStatus?.isInQueue) {
      const waitingUrl = buildWaitingForOpponentUrl(locale, gameId);
      router.push(waitingUrl);
    }
  }, [queueStatus, isCheckingQueue, gameId, locale, router]);

  if (isCheckingQueue) {
    return (
      <Container p="3">
        <Flex direction="column" align="center" justify="center" className="h-screen">
          <Text size="3" color="gray">
            Loading...
          </Text>
        </Flex>
      </Container>
    );
  }

  if (queueStatus?.isInQueue) {
    return (
      <Container p="3">
        <Flex direction="column" align="center" justify="center" className="h-screen">
          <Text size="3" color="gray">
            Redirecting to waiting room...
          </Text>
        </Flex>
      </Container>
    );
  }

  return <PlayGamePage gameId={gameId} locale={locale} />;
};

export default PlayGamePageWithQueueCheck;