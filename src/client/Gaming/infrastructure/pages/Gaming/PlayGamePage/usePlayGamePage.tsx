import {useCallback, useEffect} from "react";
import {useDispatch, useSelector} from "react-redux";
import {useRouter} from "next/navigation";
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";
import {loadDecksForGame} from "@/src/client/Gaming/application/commands/loadDecksForGame/loadDecksForGame";
import {joinMatchMakingQueue} from "@/src/client/Gaming/application/commands/joinMatchMakingQueue/joinMatchMakingQueue";
import {selectDeck} from "@/src/client/Gaming/application/commands/selectDeck/selectDeck";
import {getAvailableDecks} from "@/src/client/Gaming/application/queries/getAvailableDecks/getAvailableDecks";
import {getSelectedDeckId} from "@/src/client/Gaming/application/queries/getSelectedDeckId/getSelectedDeckId";
import {getSelectedDeck} from "@/src/client/Gaming/application/queries/getSelectedDeck/getSelectedDeck";
import {isDecksLoading} from "@/src/client/Gaming/application/queries/isDecksLoading/isDecksLoading";

export const usePlayGamePage = ({gameId, locale}: { gameId: string, locale: string }) => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const decks = useSelector(getAvailableDecks);
  const selectedDeckId = useSelector(getSelectedDeckId);
  const selectedDeck = useSelector(getSelectedDeck);
  const loadingDecks = useSelector(isDecksLoading);

  useEffect(() => {
    dispatch(loadDecksForGame({gameId, locale}));
  }, [dispatch, gameId, locale]);

  const handleStart = useCallback(async () => {
    if (!selectedDeckId) return;

    try {
      const result = await dispatch(joinMatchMakingQueue({gameId, deckId: selectedDeckId, locale})).unwrap();
      if (result.success && result.redirectUrl) {
        router.push(result.redirectUrl);
      }
    } catch (error) {
      console.error('Failed to join match making queue:', error);
    }
  }, [dispatch, gameId, selectedDeckId, locale, router]);

  const setDeckId = useCallback((deckId: string) => {
    dispatch(selectDeck({deckId}));
  }, [dispatch]);

  return {
    loadingDecks,
    deckId: selectedDeckId,
    decks,
    selectedDeckImages: selectedDeck?.images || [],
    selectedDeckName: selectedDeck?.name,
    handleStart,
    setDeckId,
  };
}
