import {FC, Suspense} from "react";
import {Container, Flex, Heading} from "@radix-ui/themes";
import {fetchQuery} from "convex/nextjs";
import {api} from "@/convex/_generated/api";
import {convexAuthNextjsToken} from "@convex-dev/auth/nextjs/server";
import GameListSkeleton from "@/src/client/Gaming/infrastructure/components/app/Gaming/GameListSkeleton/GameListSkeleton";
import GameListWithSearch from "@/src/client/Gaming/infrastructure/components/app/Gaming/GameListWithSearch/GameListWithSearch";
import {getTranslations} from "next-intl/server";

const GameListPage: FC = async () => {
  const t = await getTranslations('games');
  const viewModel = await fetchQuery(api.queries.loadGameList.endpoint, {}, {token: await convexAuthNextjsToken()});
  const games = viewModel.data ?? [];

  return (
    <Container p="4" size="4">
      <Flex direction="column" gap="6">
        <Flex direction="column" gap="3" align="center">
          <Heading size="6" className="text-center">
            {t('title')}
          </Heading>
          <Heading size="3" color="gray" className="text-center font-normal">
            {t('subtitle')}
          </Heading>
        </Flex>
        
        <Suspense fallback={<GameListSkeleton />}>
          <GameListWithSearch games={games} />
        </Suspense>
      </Flex>
    </Container>
  );
};

export default GameListPage;