import {useEffect} from "react";
import {useQuery} from "convex/react";
import {useRouter} from "next/navigation";
import {api} from "@/convex/_generated/api";
import {Id} from "@/convex/_generated/dataModel";
import {buildGameUrl} from "@/src/client/Shared/helpers/UrlBuilder/urlBuilder";

type UseMatchEndNavigationProps = {
  matchId: Id<"matches">;
  locale: string;
  gameId: Id<"games">;
};

export const useMatchEndNavigation = ({matchId, locale, gameId}: UseMatchEndNavigationProps) => {
  // @ts-ignore - long convex type
  const matchEnded = useQuery(api.queries.match.onMatchEnded, {matchId});
  const router = useRouter();

  useEffect(() => {
    if (matchEnded?.data) {
      router.push(buildGameUrl(locale, gameId));
    }
  }, [matchEnded, router, locale, gameId]);
};
