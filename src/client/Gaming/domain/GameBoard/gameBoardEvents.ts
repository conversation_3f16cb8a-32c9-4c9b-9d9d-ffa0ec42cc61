import {createAction} from '@reduxjs/toolkit';

export const cardPlacedOnBoardEvent = createAction<{
  playerId: 'player1' | 'player2';
  cardId: string;
  rowType: 'first' | 'second';
  slotIndex: number;
  placedAt: string;
}>('gameBoard/cardPlaced');

export const cardRemovedFromBoardEvent = createAction<{
  playerId: 'player1' | 'player2';
  rowType: 'first' | 'second';
  slotIndex: number;
}>('gameBoard/cardRemoved');

export const boardClearedEvent = createAction('gameBoard/boardCleared');