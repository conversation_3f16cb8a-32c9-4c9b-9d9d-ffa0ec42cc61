import {createReducer} from '@reduxjs/toolkit';
import {
  cardPlacedOnBoardEvent,
  cardRemovedFromBoardEvent,
  boardClearedEvent,
} from './gameBoardEvents';

export interface PlacedCard {
  cardId: string;
  placedAt: string;
  slotIndex: number;
  rowType: 'first' | 'second';
}

export interface PlayerBoard {
  firstRow: (PlacedCard | null)[];
  secondRow: (PlacedCard | null)[];
}

export interface GameBoardState {
  player1Board: PlayerBoard;
  player2Board: PlayerBoard;
  maxSlotsPerRow: number;
}

export const initialGameBoardState: GameBoardState = {
  player1Board: {
    firstRow: Array(8).fill(null),
    secondRow: Array(8).fill(null),
  },
  player2Board: {
    firstRow: Array(8).fill(null),
    secondRow: Array(8).fill(null),
  },
  maxSlotsPerRow: 8,
};

export const gameBoardReducer = createReducer(
  initialGameBoardState,
  (builder) =>
    builder
      .addCase(cardPlacedOnBoardEvent, (state, {payload}) => {
        const {playerId, cardId, rowType, slotIndex, placedAt} = payload;
        const board = state[`${playerId}Board`];
        const row = rowType === 'first' ? board.firstRow : board.secondRow;
        
        if (slotIndex >= 0 && slotIndex < state.maxSlotsPerRow) {
          row[slotIndex] = {
            cardId,
            placedAt,
            slotIndex,
            rowType,
          };
        }
      })
      .addCase(cardRemovedFromBoardEvent, (state, {payload}) => {
        const {playerId, rowType, slotIndex} = payload;
        const board = state[`${playerId}Board`];
        const row = rowType === 'first' ? board.firstRow : board.secondRow;
        
        if (slotIndex >= 0 && slotIndex < state.maxSlotsPerRow) {
          row[slotIndex] = null;
        }
      })
      .addCase(boardClearedEvent, (state) => {
        state.player1Board.firstRow.fill(null);
        state.player1Board.secondRow.fill(null);
        state.player2Board.firstRow.fill(null);
        state.player2Board.secondRow.fill(null);
      }),
);