import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {SyncBoardStateRequest} from './syncBoardStateRequest';
import {boardStateSyncedEvent} from '@/src/client/Gaming/domain/GameBoard/gameBoardEvents';

export const syncBoardState = createAsyncThunk<void, SyncBoardStateRequest, {state: RootState}>(
  'gaming/syncBoardState',
  async ({matchId}, {dispatch}) => {
    try {
      console.log('Syncing board state for match:', matchId);
      
      dispatch(boardStateSyncedEvent({
        boardState: {
          player1Board: {
            firstRow: Array(8).fill(null),
            secondRow: Array(8).fill(null),
          },
          player2Board: {
            firstRow: Array(8).fill(null),
            secondRow: Array(8).fill(null),
          }
        }
      }));
    } catch (error) {
      console.error('Failed to sync board state:', error);
    }
  }
);