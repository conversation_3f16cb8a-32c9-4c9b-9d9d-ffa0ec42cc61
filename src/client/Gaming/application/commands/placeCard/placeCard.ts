import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {GameBoard} from '@/src/client/Gaming/domain/GameBoard/GameBoard';
import {PlayerHands} from '@/src/client/Gaming/domain/PlayerHands/PlayerHands';
import {PlaceCardRequest} from '@/src/client/Gaming/application/commands/placeCard/placeCardRequest';

const getCurrentPlayerId = (): 'player1' | 'player2' => {
  return 'player1';
};

export const placeCard = createAsyncThunk<void, PlaceCardRequest & {matchId: string}, {state: RootState; extra: ThunkExtra}>(
  'gaming/placeCard',
  async ({cardId, rowType, slotIndex, matchId}, {dispatch, getState, extra: {matchService}}) => {
    // First, try to place the card on the server
    const result = await matchService.playCard(matchId, cardId, rowType, slotIndex);
    
    if (result.error) {
      throw new Error(result.error);
    }

    // If successful, update local state optimistically
    const state = getState();
    const playerId = getCurrentPlayerId();
    const gameBoard = GameBoard.fromState(state.gameBoard);
    const playerHands = PlayerHands.fromState(state.playerHands);

    gameBoard.placeCard(playerId, cardId, rowType, slotIndex);
    playerHands.removeCardFromHand(playerId, cardId);

    gameBoard.getDomainEvents().forEach(dispatch);
    playerHands.getDomainEvents().forEach(dispatch);
  }
);