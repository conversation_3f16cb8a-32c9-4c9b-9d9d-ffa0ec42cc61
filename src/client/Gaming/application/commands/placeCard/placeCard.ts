import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {GameBoard} from '@/src/client/Gaming/domain/GameBoard/GameBoard';
import {PlayerHands} from '@/src/client/Gaming/domain/PlayerHands/PlayerHands';
import {PlaceCardRequest} from '@/src/client/Gaming/application/commands/placeCard/placeCardRequest';

const getCurrentPlayerId = (): 'player1' | 'player2' => {
  return 'player1';
};

export const placeCard = createAsyncThunk<void, PlaceCardRequest, {state: RootState}>(
  'gaming/placeCard',
  async ({cardId, rowType, slotIndex}, {dispatch, getState}) => {
    const state = getState();
    const playerId = getCurrentPlayerId();
    const gameBoard = GameBoard.fromState(state.gameBoard);
    const playerHands = PlayerHands.fromState(state.playerHands);

    gameBoard.placeCard(playerId, cardId, rowType, slotIndex);
    playerHands.removeCardFromHand(playerId, cardId);

    gameBoard.getDomainEvents().forEach(dispatch);
    playerHands.getDomainEvents().forEach(dispatch);
  }
);