export type LeaveMatchResult = {
  error?: string;
};

export type PlayCardResult = {
  error?: string;
};

export type BoardStateResult = {
  boardState?: {
    player1Board: {
      firstRow: any[];
      secondRow: any[];
    };
    player2Board: {
      firstRow: any[];
      secondRow: any[];
    };
  };
  error?: string;
};

export interface MatchService {
  leaveMatch(matchId: string): Promise<LeaveMatchResult>;
  playCard(matchId: string, cardId: string, rowType: 'first' | 'second', slotIndex: number): Promise<PlayCardResult>;
  getBoardState(matchId: string): Promise<BoardStateResult>;
};
