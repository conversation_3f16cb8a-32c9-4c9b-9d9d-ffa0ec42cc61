import {render} from '@testing-library/react';
import {Mock} from 'vitest';

interface TestTexture {
  image: {height: number};
  repeat: {set: Mock};
  offset: {set: Mock};
  needsUpdate: boolean;
  clone: () => TestTexture;
}

const clones: TestTexture[] = [];

const createTexture = (): TestTexture => {
  return {
    image: {height: 512},
    repeat: {set: vi.fn()},
    offset: {set: vi.fn()},
    needsUpdate: false,
    clone: vi.fn(() => {
      const cloned = createTexture();
      clones.push(cloned);
      return cloned;
    }),
  };
};

const frontTexture = createTexture();
const backTexture = createTexture();

vi.mock('@react-three/fiber', () => ({
  useLoader: vi.fn(() => [frontTexture, backTexture]),
  useThree: (
    selector: (state: {gl: {capabilities: {getMaxAnisotropy: () => number}}}) => unknown,
  ) => selector({gl: {capabilities: {getMaxAnisotropy: () => 16}}}),
}));

vi.mock(
  '@/src/client/Gaming/infrastructure/components/app/3d/CardEffects/CardEffects',
  () => ({
    __esModule: true,
    default: () => null,
  }),
);

vi.mock('three', () => ({
  LinearFilter: 0,
  LinearMipmapLinearFilter: 0,
  SRGBColorSpace: 0,
  TextureLoader: class {},
  Texture: class {},
}));

vi.mock('three-stdlib', () => ({
  RoundedBoxGeometry: class {},
}));

describe('Card', () => {
  describe('When the same image is used with and without cropping', () => {
    it('should not crop the card without visible height', async () => {
      // Arrange
      const {default: Card} = await import('@/src/client/Gaming/infrastructure/components/app/3d/Card/Card');

      // Act
      render(
        <>
          <Card id="1" position={[0,0,0]} visibleHeight={340} />
          <Card id="1" position={[0,0,0]} />
        </>
      );

      // Assert
      expect(clones[0].repeat.set).toHaveBeenCalledTimes(1);
      expect(clones[1].repeat.set).not.toHaveBeenCalled();
    });
  });

  describe('When no id is provided', () => {
    it('should use the back image for both sides', async () => {
      // Arrange
      const {default: Card} = await import('@/src/client/Gaming/infrastructure/components/app/3d/Card/Card');
      const {useLoader} = await import('@react-three/fiber');

      // Act
      render(<Card position={[0,0,0]} />);

      // Assert
      expect(useLoader).toHaveBeenCalledWith(expect.anything(), [
        '/game-assets/cards/back-small.png',
        '/game-assets/cards/back-small.png',
      ]);
    });
  });
});
