import {render, screen} from '@testing-library/react';
import {Provider} from 'react-redux';
import {NextIntlClientProvider} from 'next-intl';
import GameDetailsPage from '@/src/client/Gaming/infrastructure/pages/Gaming/GameDetailsPage/GameDetailsPage';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {useQuery} from 'convex/react';

vi.mock('convex/react');

describe('GameDetailsPage', () => {
  describe('When initialized', () => {
    it('should display loading indicator', () => {
      // Arrange
      vi.mocked(useQuery).mockReturnValue(undefined);
      const store = createTestingStore();

      // Act
      render(
        <Provider store={store}>
          <NextIntlClientProvider locale="en" messages={{games: {gameDetails: {loading: 'Loading game details...'}}}}>
            <GameDetailsPage locale="en" gameId="g1" />
          </NextIntlClientProvider>
        </Provider>
      );

      // Assert
      expect(screen.getByText('Loading game details...')).toBeTruthy();
    });
  });
});
