import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {leaveMatch} from '@/src/client/Gaming/application/commands/leaveMatch/leaveMatch';
import {FakeMatchService} from '@/src/client/Gaming/infrastructure/services/Match/FakeMatchService';
import {getMatchError} from '@/src/client/Gaming/application/queries/getMatchError/getMatchError';

describe('leaveMatch', () => {
  describe('When leaving match successfully', () => {
    it('should return success and clear any errors', async () => {
      // Arrange
      const matchService = new FakeMatchService();
      const {dispatch, getState} = createTestingStore({}, {matchService});

      // Act
      const result = await dispatch(leaveMatch({matchId: 'match1'})).unwrap();

      // Assert
      expect(result.success).toBe(true);
      expect(getMatchError(getState())).toBeNull();
    });
  });

  describe('When leaving match fails', () => {
    it('should return failure and set error', async () => {
      // Arrange
      const matchService = new FakeMatchService({error: 'Cannot leave match'});
      const {dispatch, getState} = createTestingStore({}, {matchService});

      // Act
      const result = await dispatch(leaveMatch({matchId: 'match1'})).unwrap();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Cannot leave match');
      expect(getMatchError(getState())).toBe('Cannot leave match');
    });
  });

  describe('When service throws an error', () => {
    it('should return failure and set error', async () => {
      // Arrange
      const matchService = new FakeMatchService({}, {}, true);
      const {dispatch, getState} = createTestingStore({}, {matchService});

      // Act
      const result = await dispatch(leaveMatch({matchId: 'match1'})).unwrap();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to leave match');
      expect(getMatchError(getState())).toBe('Failed to leave match');
    });
  });
});
