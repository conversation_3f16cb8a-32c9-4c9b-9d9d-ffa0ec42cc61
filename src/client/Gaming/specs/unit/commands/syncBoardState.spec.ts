import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {syncBoardState} from '@/src/client/Gaming/application/commands/syncBoardState/syncBoardState';
import {getGameBoard} from '@/src/client/Gaming/application/queries/getGameBoard/getGameBoard';
import {FakeMatchService} from '@/src/client/Gaming/infrastructure/services/Match/FakeMatchService';

describe('When syncing board state successfully', () => {
  it('should update local board state with server data', async () => {
    // Arrange
    const mockBoardState = {
      player1Board: {
        firstRow: [
          {cardId: 'card-123', placedAt: '2023-01-01T00:00:00Z', slotIndex: 0, rowType: 'first'},
          null, null, null, null, null, null, null
        ],
        secondRow: Array(8).fill(null),
      },
      player2Board: {
        firstRow: Array(8).fill(null),
        secondRow: [
          null, null, 
          {cardId: 'card-456', placedAt: '2023-01-01T00:00:00Z', slotIndex: 2, rowType: 'second'},
          null, null, null, null, null
        ],
      }
    };
    
    const matchService = new FakeMatchService({}, {}, {boardState: mockBoardState});
    const {dispatch, getState} = createTestingStore({}, {matchService});

    // Act
    await dispatch(syncBoardState({matchId: 'match-123'}));

    // Assert
    const gameBoard = getGameBoard(getState());
    expect(gameBoard.player1Board.firstRow[0]).toEqual(mockBoardState.player1Board.firstRow[0]);
    expect(gameBoard.player2Board.secondRow[2]).toEqual(mockBoardState.player2Board.secondRow[2]);
  });
});

describe('When sync board state fails with error', () => {
  it('should not update local board state', async () => {
    // Arrange
    const matchService = new FakeMatchService({}, {}, {error: 'Network error'});
    const {dispatch, getState} = createTestingStore({}, {matchService});
    const initialBoard = getGameBoard(getState());

    // Act
    await dispatch(syncBoardState({matchId: 'match-123'}));

    // Assert
    const finalBoard = getGameBoard(getState());
    expect(finalBoard).toEqual(initialBoard);
  });
});

describe('When sync board state throws exception', () => {
  it('should not update local board state', async () => {
    // Arrange
    const matchService = new FakeMatchService({}, {}, {}, true);
    const {dispatch, getState} = createTestingStore({}, {matchService});
    const initialBoard = getGameBoard(getState());

    // Act
    await dispatch(syncBoardState({matchId: 'match-123'}));

    // Assert
    const finalBoard = getGameBoard(getState());
    expect(finalBoard).toEqual(initialBoard);
  });
});