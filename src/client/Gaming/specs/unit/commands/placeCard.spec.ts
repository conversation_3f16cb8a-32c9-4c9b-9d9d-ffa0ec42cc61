import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {placeCard} from '@/src/client/Gaming/application/commands/placeCard/placeCard';
import {updatePlayerHand} from '@/src/client/Gaming/application/commands/updatePlayerHand/updatePlayerHand';
import {getPlayer1Hand} from '@/src/client/Gaming/application/queries/getPlayerHands/getPlayerHands';
import {FakeMatchService} from '@/src/client/Gaming/infrastructure/services/Match/FakeMatchService';

describe('placeCard', () => {
  describe('When placing card successfully', () => {
    it('should remove card from hand', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-123', 'card-456']}));

      // Act
      await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123',
        currentPlayerPosition: 'player1'
      }));

      // Assert
      expect(getPlayer1Hand(getState())).toEqual(['card-456']);
    });
  });

  describe('When placing card with player2 position', () => {
    it('should call server with correct parameters', async () => {
      // Arrange
      const {dispatch} = createTestingStore();

      // Act
      const result = await dispatch(placeCard({
        cardId: 'card-789',
        rowType: 'second',
        slotIndex: 3,
        matchId: 'match-123',
        currentPlayerPosition: 'player2'
      }));

      // Assert
      expect(result.type).toBe('gaming/placeCard/fulfilled');
    });
  });

  describe('When matchService returns error', () => {
    it('should throw error and not remove card from hand', async () => {
      // Arrange
      const matchService = new FakeMatchService({}, {error: 'Server error'}, {}, false);
      const {dispatch, getState} = createTestingStore({}, {matchService});
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-123', 'card-456']}));

      // Act
      const result = await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123',
        currentPlayerPosition: 'player1'
      }));

      // Assert
      expect(result.type).toBe('gaming/placeCard/rejected');
      expect(getPlayer1Hand(getState())).toEqual(['card-123', 'card-456']);
    });
  });

  describe('When placing card not in player hand', () => {
    it('should throw error during hand removal', async () => {
      // Arrange
      const {dispatch} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-456']}));

      // Act
      const result = await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123',
        currentPlayerPosition: 'player1'
      }));

      // Assert
      expect(result.type).toBe('gaming/placeCard/rejected');
    });
  });
});