import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {placeCard} from '@/src/client/Gaming/application/commands/placeCard/placeCard';
import {updatePlayerHand} from '@/src/client/Gaming/application/commands/updatePlayerHand/updatePlayerHand';
import {getPlayer1Hand} from '@/src/client/Gaming/application/queries/getPlayerHands/getPlayerHands';
import {getGameBoard} from '@/src/client/Gaming/application/queries/getGameBoard/getGameBoard';

describe('placeCard', () => {
  describe('When placing card in empty slot', () => {
    it('should place card on board and remove from hand', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-123', 'card-456']}));

      // Act
      await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123'
      }));

      // Assert
      const gameBoard = getGameBoard(getState());
      const placedCard = gameBoard.player1Board.firstRow[0];
      expect(placedCard).not.toBeNull();
      expect(placedCard!.cardId).toBe('card-123');
      expect(placedCard!.rowType).toBe('first');
      expect(placedCard!.slotIndex).toBe(0);
      expect(getPlayer1Hand(getState())).toEqual(['card-456']);
    });
  });

  describe('When placing card in player1 second row', () => {
    it('should place card on board and remove from hand', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-789']}));

      // Act
      await dispatch(placeCard({
        cardId: 'card-789',
        rowType: 'second',
        slotIndex: 3,
        matchId: 'match-123'
      }));

      // Assert
      const gameBoard = getGameBoard(getState());
      const placedCard = gameBoard.player1Board.secondRow[3];
      expect(placedCard).not.toBeNull();
      expect(placedCard!.cardId).toBe('card-789');
      expect(placedCard!.rowType).toBe('second');
      expect(placedCard!.slotIndex).toBe(3);
    });
  });

  describe('When placing card in occupied slot', () => {
    it('should throw error', async () => {
      // Arrange
      const {dispatch} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-123', 'card-456']}));
      await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123'
      }));

      // Act
      const result = await dispatch(placeCard({
        cardId: 'card-456',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123'
      }));

      // Assert
      expect(result.type).toBe('gaming/placeCard/rejected');
    });
  });

  describe('When placing card with invalid slot index', () => {
    it('should throw error', async () => {
      // Arrange
      const {dispatch} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-123']}));

      // Act
      const result = await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: -1,
        matchId: 'match-123'
      }));

      // Assert
      expect(result.type).toBe('gaming/placeCard/rejected');
    });
  });

  describe('When placing card not in hand', () => {
    it('should throw error', async () => {
      // Arrange
      const {dispatch} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-456']}));

      // Act
      const result = await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123'
      }));

      // Assert
      expect(result.type).toBe('gaming/placeCard/rejected');
    });
  });

  describe('When placing card at slot index 7', () => {
    it('should place card successfully', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-123']}));

      // Act
      await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 7,
        matchId: 'match-123'
      }));

      // Assert
      const gameBoard = getGameBoard(getState());
      const placedCard = gameBoard.player1Board.firstRow[7];
      expect(placedCard).not.toBeNull();
      expect(placedCard!.cardId).toBe('card-123');
      expect(getPlayer1Hand(getState())).toEqual([]);
    });
  });

  describe('When placing card at slot index 8', () => {
    it('should throw error', async () => {
      // Arrange
      const {dispatch} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-123']}));

      // Act
      const result = await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 8,
        matchId: 'match-123'
      }));

      // Assert
      expect(result.type).toBe('gaming/placeCard/rejected');
    });
  });
});