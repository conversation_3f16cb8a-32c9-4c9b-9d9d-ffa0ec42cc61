import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {placeCard} from '@/src/client/Gaming/application/commands/placeCard/placeCard';
import {removeCard} from '@/src/client/Gaming/application/commands/removeCard/removeCard';
import {updatePlayerHand} from '@/src/client/Gaming/application/commands/updatePlayerHand/updatePlayerHand';
import {getPlayer1Hand} from '@/src/client/Gaming/application/queries/getPlayerHands/getPlayerHands';
import {getGameBoard} from '@/src/client/Gaming/application/queries/getGameBoard/getGameBoard';

describe('removeCard', () => {
  describe('When removing card from occupied slot', () => {
    it('should remove card from board and add to hand', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-123', 'card-456']}));
      await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 2,
        matchId: 'match-123'
      }));

      // Act
      await dispatch(removeCard({
        rowType: 'first',
        slotIndex: 2
      }));

      // Assert
      const gameBoard = getGameBoard(getState());
      expect(gameBoard.player1Board.firstRow[2]).toBeNull();
      expect(getPlayer1Hand(getState())).toEqual(['card-456', 'card-123']);
    });
  });

  describe('When removing card from player1 second row', () => {
    it('should remove card from board and add to hand', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-789']}));
      await dispatch(placeCard({
        cardId: 'card-789',
        rowType: 'second',
        slotIndex: 5,
        matchId: 'match-123'
      }));

      // Act
      await dispatch(removeCard({
        rowType: 'second',
        slotIndex: 5
      }));

      // Assert
      const gameBoard = getGameBoard(getState());
      expect(gameBoard.player1Board.secondRow[5]).toBeNull();
    });
  });

  describe('When removing card from empty slot', () => {
    it('should throw error', async () => {
      // Arrange
      const {dispatch} = createTestingStore();

      // Act
      const result = await dispatch(removeCard({
        rowType: 'first',
        slotIndex: 0
      }));

      // Assert
      expect(result.type).toBe('gaming/removeCard/rejected');
    });
  });

  describe('When removing card from last slot', () => {
    it('should remove card successfully', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-123']}));
      await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'second',
        slotIndex: 7,
        matchId: 'match-123'
      }));

      // Act
      await dispatch(removeCard({
        rowType: 'second',
        slotIndex: 7
      }));

      // Assert
      const gameBoard = getGameBoard(getState());
      expect(gameBoard.player1Board.secondRow[7]).toBeNull();
      expect(getPlayer1Hand(getState())).toEqual(['card-123']);
    });
  });

  describe('When removing card and placing again', () => {
    it('should work correctly', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-123']}));
      await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123'
      }));
      await dispatch(removeCard({
        rowType: 'first',
        slotIndex: 0
      }));

      // Act
      await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 1,
        matchId: 'match-123'
      }));

      // Assert
      const gameBoard = getGameBoard(getState());
      expect(gameBoard.player1Board.firstRow[0]).toBeNull();
      expect(gameBoard.player1Board.firstRow[1]).not.toBeNull();
      expect(gameBoard.player1Board.firstRow[1]!.cardId).toBe('card-123');
      expect(getPlayer1Hand(getState())).toEqual([]);
    });
  });

  describe('When multiple cards placed and removing middle card', () => {
    it('should only remove specified card', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-1', 'card-2', 'card-3']}));
      await dispatch(placeCard({
        cardId: 'card-1',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123'
      }));
      await dispatch(placeCard({
        cardId: 'card-2',
        rowType: 'first',
        slotIndex: 1,
        matchId: 'match-123'
      }));
      await dispatch(placeCard({
        cardId: 'card-3',
        rowType: 'first',
        slotIndex: 2,
        matchId: 'match-123'
      }));

      // Act
      await dispatch(removeCard({
        rowType: 'first',
        slotIndex: 1
      }));

      // Assert
      const gameBoard = getGameBoard(getState());
      expect(gameBoard.player1Board.firstRow[0]!.cardId).toBe('card-1');
      expect(gameBoard.player1Board.firstRow[1]).toBeNull();
      expect(gameBoard.player1Board.firstRow[2]!.cardId).toBe('card-3');
      expect(getPlayer1Hand(getState())).toEqual(['card-2']);
    });
  });
});