import {describe, it, expect} from 'vitest';

describe('TextureCache URL handling', () => {
  describe('When processing card IDs', () => {
    it('should handle simple card IDs correctly', () => {
      const cardId = '72';
      const expectedUrl = '/game-assets/cards/en/thumbnail/72.jpg';
      
      // Test the logic that would be in TextureCache
      const url = cardId.startsWith('/game-assets/') || cardId.startsWith('http') 
        ? cardId 
        : `/game-assets/cards/en/thumbnail/${cardId}.jpg`;
      
      expect(url).toBe(expectedUrl);
    });

    it('should handle full path card IDs correctly', () => {
      const cardId = '/game-assets/cards/en/thumbnail/72.jpg';
      const expectedUrl = '/game-assets/cards/en/thumbnail/72.jpg';
      
      // Test the logic that would be in TextureCache
      const url = cardId.startsWith('/game-assets/') || cardId.startsWith('http') 
        ? cardId 
        : `/game-assets/cards/en/thumbnail/${cardId}.jpg`;
      
      expect(url).toBe(expectedUrl);
    });

    it('should extract simple ID from full path correctly', () => {
      const fullPath = '/game-assets/cards/en/thumbnail/72.jpg';
      const expectedSimpleId = '72';
      
      // Test the logic that would be in TextureCache
      const simpleId = fullPath.includes('/game-assets/cards/en/thumbnail/') 
        ? fullPath.replace('/game-assets/cards/en/thumbnail/', '').replace('.jpg', '')
        : fullPath;
      
      expect(simpleId).toBe(expectedSimpleId);
    });

    it('should not double-process paths', () => {
      const alreadyProcessedPath = '/game-assets/cards/en/thumbnail/72.jpg';
      
      // This should not create a double path
      const url = alreadyProcessedPath.startsWith('/game-assets/') || alreadyProcessedPath.startsWith('http') 
        ? alreadyProcessedPath 
        : `/game-assets/cards/en/thumbnail/${alreadyProcessedPath}.jpg`;
      
      expect(url).toBe('/game-assets/cards/en/thumbnail/72.jpg');
      expect(url).not.toContain('//game-assets/cards/en/thumbnail/');
    });
  });
});
