import {renderHook, waitFor} from '@testing-library/react';
import {Provider} from 'react-redux';
import {FC} from 'react';
import {
  useWaitingForOpponentPage
} from '@/src/client/Gaming/infrastructure/pages/Gaming/WaitingForOpponentPage/useWaitingForOpponentPage';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {FakeMatchMakingService} from '@/src/client/Gaming/infrastructure/services/MatchMaking/FakeMatchMakingService';

const pushMock = vi.fn();
vi.mock('next/navigation', () => ({
  useRouter: () => ({push: pushMock}),
}));

beforeEach(() => {
  pushMock.mockClear();
});

const createWrapper = (store: ReturnType<typeof createTestingStore>) => {
  const Wrapper: FC<{ children: React.ReactNode }> = ({children}) => (
    <Provider store={store}>{children}</Provider>
  );
  return Wrapper;
};

describe('useWaitingForOpponentPage', () => {
  describe('When cancelling the search', () => {
    it('should navigate back to the play page', async () => {
      // Arrange
      const matchMakingService = new FakeMatchMakingService();
      const store = createTestingStore({}, {matchMakingService});
      const {result} = renderHook(
        () => useWaitingForOpponentPage({gameId: 'g1', locale: 'en'}),
        {wrapper: createWrapper(store)}
      );

      await waitFor(() => {
        expect(result.current).toBeTruthy();
      });

      // Act
      await result.current.handleCancel();

      // Assert
      expect(pushMock).toHaveBeenCalledWith('/en/games/g1/play');
    });
  });
});
