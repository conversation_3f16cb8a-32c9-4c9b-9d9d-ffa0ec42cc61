import {renderHook} from '@testing-library/react';
import {Provider} from 'react-redux';
import {useBoardStateSync} from '@/src/client/Gaming/infrastructure/hooks/useBoardStateSync/useBoardStateSync';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {getGameBoard} from '@/src/client/Gaming/application/queries/getGameBoard/getGameBoard';

const mockUseQuery = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: (...args: any[]) => mockUseQuery(...args)
}));

describe('When board state data is available', () => {
  it('should dispatch board state sync event', () => {
    // Arrange
    const mockBoardState = {
      player1Board: {
        firstRow: [{cardId: 'card-123', placedAt: '2023-01-01T00:00:00Z', slotIndex: 0, rowType: 'first'}],
        secondRow: Array(8).fill(null),
      },
      player2Board: {
        firstRow: Array(8).fill(null),
        secondRow: Array(8).fill(null),
      }
    };

    mockUseQuery.mockReturnValue({boardState: mockBoardState});
    const store = createTestingStore();

    // Act
    renderHook(() => useBoardStateSync('match-123'), {
      wrapper: ({children}) => <Provider store={store}>{children}</Provider>
    });

    // Assert
    const gameBoard = getGameBoard(store.getState());
    expect(gameBoard.player1Board.firstRow[0]).toEqual(mockBoardState.player1Board.firstRow[0]);
  });
});

describe('When match ID is undefined', () => {
  it('should not call useQuery', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);
    const store = createTestingStore();

    // Act
    const {result} = renderHook(() => useBoardStateSync(undefined), {
      wrapper: ({children}) => <Provider store={store}>{children}</Provider>
    });

    // Assert
    expect(mockUseQuery).toHaveBeenCalledWith(undefined, undefined);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(false);
  });
});

describe('When board state data is loading', () => {
  it('should return loading state', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);
    const store = createTestingStore();

    // Act
    const {result} = renderHook(() => useBoardStateSync('match-123'), {
      wrapper: ({children}) => <Provider store={store}>{children}</Provider>
    });

    // Assert
    expect(result.current.isLoading).toBe(true);
    expect(result.current.error).toBe(false);
  });
});