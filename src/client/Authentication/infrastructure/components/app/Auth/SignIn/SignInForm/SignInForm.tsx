import {Flex, Heading, Text} from '@radix-ui/themes';
import Image from 'next/image';
import {type FC, memo} from 'react';
import ShiningCard from "@/src/client/Shared/components/ShiningCard/ShiningCard";
import SignInButton from "@/src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton";

const SignInForm: FC = () => (
  <ShiningCard selected>
    <Flex direction="column" gap="4" p="6" minWidth={{sm: '320px'}}>
      <Flex justify="center" align="center">
        <Image src="/logos/evygames-logo.png" alt="logo" width="250" height="250"/>
      </Flex>

      <Flex direction="column" align="center">
        <Heading size="8" mb="3">
          EvyGames
        </Heading>
        <Text size="3" color="gray">
          Sign in or create an account
        </Text>
      </Flex>
      <SignInButton/>
    </Flex>
  </ShiningCard>
);

export default memo(SignInForm);
