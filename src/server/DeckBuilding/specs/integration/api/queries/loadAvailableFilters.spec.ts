import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {createFakeGame} from "@/src/server/Shared/specs/helpers/requests/createFakeGame";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";
import {ADMIN_IDENTITY, SOPHIE_APP_USER, SOPHIE_IDENTITY} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {api} from "@/convex/_generated/api";
import {LORCANA} from "@/src/server/Shared/specs/helpers/fakes/fakeGames";

describe('loadAvailableFilters', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const test = convexTest(schema);
    asAdmin = test.withIdentity(ADMIN_IDENTITY);
    asSophie = test.withIdentity(SOPHIE_IDENTITY);
    createAppUser(asAdmin, SOPHIE_APP_USER);
  });

  describe('When filters exist for the game', () => {
    it('should return them ordered by their order field', async () => {
      // Arrange
      const gameId = await createFakeGame(LORCANA, asAdmin) as Id<'games'>;
      await asAdmin.run(ctx => ctx.db.insert('availableFilters', {
        gameId,
        text: 'Inkable',
        name: 'INKABLE',
        dataProperty: 'inkable',
        dataType: 'boolean',
        value: true,
        order: 2,
      }));
      await asAdmin.run(ctx => ctx.db.insert('availableFilters', {
        gameId,
        text: 'Amber',
        name: 'AMBER',
        dataProperty: 'color',
        dataType: 'string',
        value: 'AMBER',
        order: 1,
      }));

      // Act
      const result = await loadAvailableFilters(asSophie, gameId);

      // Assert
      expect(result.data!.availableFilters.map(f => f.name)).toEqual(['AMBER', 'INKABLE']);
    });
  });

  function loadAvailableFilters(user: TestConvexForDataModel<DataModel>, gameId: Id<'games'>) {
    return user.query(api.queries.filters.loadAvailableFilters, {gameId});
  }
});
