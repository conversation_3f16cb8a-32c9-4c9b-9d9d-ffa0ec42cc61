export interface MatchProps {
  id?: string;
  gameId: string;
  players: string[];
  status: string;
  winner?: string;
  currentMulliganRound?: number;
}

export type MatchSnapshot = Omit<MatchProps, 'id'> & { id: string };

export class Match {
  private props: MatchProps;
  constructor(props: MatchProps) {
    this.props = props;
  }

  static create(props: MatchProps) {
    return new Match(props);
  }

  static fromSnapshot(snapshot: MatchSnapshot): Match {
    return new Match({...snapshot});
  }

  toSnapshot(): MatchSnapshot {
    return {
      id: this.props.id!,
      gameId: this.props.gameId,
      players: this.props.players,
      status: this.props.status,
      winner: this.props.winner,
      currentMulliganRound: this.props.currentMulliganRound,
    };
  }

  getId() { return this.props.id!; }
  getGameId() { return this.props.gameId; }
  getPlayers() { return [...this.props.players]; }
  getStatus() { return this.props.status; }
  getWinner() { return this.props.winner; }
  getCurrentMulliganRound() { return this.props.currentMulliganRound || 1; }

  isFinished() { return this.props.status === 'finished'; }

  finishMatchWithWinner(winner: string) {
    this.props.winner = winner;
    this.props.status = 'finished';
  }

  getOpponentOf(playerId: string): string {
    return this.props.players.find(p => p !== playerId)!;
  }

  startMulliganPhase(): void {
    if (this.props.status !== 'setup') {
      throw new Error(`Cannot start mulligan from ${this.props.status} state`);
    }
    this.props.status = 'waiting_for_mulligan';
    this.props.currentMulliganRound = 1;
  }

  advanceToNextMulliganRound(): void {
    if (this.props.status !== 'waiting_for_mulligan') {
      throw new Error(`Cannot advance mulligan round from ${this.props.status} state`);
    }
    this.props.currentMulliganRound = (this.props.currentMulliganRound || 1) + 1;
  }

  completeMulliganPhase(): void {
    this.props.status = 'active';
  }

  makeReadyForPlay(): void {
    this.props.status = 'active';
  }

  isWaitingForMulligan(): boolean {
    return this.props.status === 'waiting_for_mulligan';
  }

  startDirectly(): void {
    if (this.props.status !== 'setup') {
      throw new Error(`Cannot start directly from ${this.props.status} state`);
    }
    this.props.status = 'active';
  }
}
