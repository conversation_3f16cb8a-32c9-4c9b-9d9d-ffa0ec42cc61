import {GenericMutationCtx} from "convex/server";
import {DataModel, Doc, Id } from "@/convex/_generated/dataModel";
import {MatchRepository} from "@/src/server/Gaming/application/ports/MatchRepository";
import {Match, MatchProps} from "@/src/server/Gaming/domain/Match/Match";

export class ConvexMatchRepository implements MatchRepository {
  private readonly ctx: GenericMutationCtx<DataModel>;

  constructor(ctx: GenericMutationCtx<DataModel>) {
    this.ctx = ctx;
  }

  async save(match: Match) {
    const props = (match as unknown as {props: MatchProps}).props;
    if (props.id) {
      const data = match.toSnapshot();
      await this.ctx.db.patch(data.id as Id<'matches'>, {
        status: data.status,
        winner: data.winner,
      } as Partial<Doc<'matches'>>);
      return data.id;
    }

    const id = await this.ctx.db.insert(
      'matches',
      {
        gameId: props.gameId as Id<'games'>,
        players: props.players,
        status: props.status,
        createdAt: Date.now(),
        winner: props.winner,
      } as unknown as Omit<Doc<'matches'>, '_id' | '_creationTime'>
    );
    props.id = id as string;
    return id;
  }

  async findById(id: string): Promise<Match | null> {
    const doc = await this.ctx.db.get(id as Id<'matches'>);
    return doc ? Match.fromSnapshot({ id: doc._id, ...doc }) : null;
  }
}
