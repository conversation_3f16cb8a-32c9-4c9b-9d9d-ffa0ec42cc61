import {convexTest, TestConvexForDataModel} from "convex-test";
import schema from "@/convex/schema";
import {api} from "@/convex/_generated/api";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  SOPHIE_APP_USER,
  SOPHIE_IDENTITY
} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";
import {createFakeGame} from "@/src/server/Shared/specs/helpers/requests/createFakeGame";
import {GOBELIN_DES_BOIS} from "@/src/server/Shared/specs/helpers/fakes/fakeGames";
import {DataModel, Id} from "@/convex/_generated/dataModel";

describe('playCard', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let john: TestConvexForDataModel<DataModel>;
  let sophie: TestConvexForDataModel<DataModel>;
  let gameId: Id<"games">;
  let matchId: Id<"matches">;

  beforeEach(async () => {
    const test = convexTest(schema);
    asAdmin = test.withIdentity(ADMIN_IDENTITY);
    john = test.withIdentity(JOHN_IDENTITY);
    sophie = test.withIdentity(SOPHIE_IDENTITY);

    await createAppUser(asAdmin, JOHN_APP_USER);
    await createAppUser(asAdmin, SOPHIE_APP_USER);
    gameId = await createFakeGame(GOBELIN_DES_BOIS, asAdmin);

    matchId = await asAdmin.run(ctx =>
      ctx.db.insert('matches', {
        gameId,
        players: [JOHN_APP_USER.appUserId, SOPHIE_APP_USER.appUserId],
        status: 'active',
        createdAt: Date.now(),
      })
    );
  });

  describe('When a player places a card on the board', () => {
    it('should save the board state and make it available to other players', async () => {
      // Act
      await john.mutation(api.mutations.playCard.endpoint, {
        matchId,
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 2
      });

      // Assert - Check that the board state was saved in the database
      const match = await asAdmin.run(ctx => ctx.db.get(matchId));
      expect(match).toBeDefined();
      expect(match!.boardState).toBeDefined();
      expect(match!.boardState!.player1Board.firstRow[2]).toEqual({
        cardId: 'card-123',
        placedAt: expect.any(String),
        slotIndex: 2,
        rowType: 'first'
      });

      // Assert - Check that the board state is available via the query
      const boardStateQuery = await sophie.query(api.queries.matchBoardState.getBoardState, {
        matchId
      });
      expect(boardStateQuery).toBeDefined();
      expect(boardStateQuery!.boardState.player1Board.firstRow[2]).toEqual({
        cardId: 'card-123',
        placedAt: expect.any(String),
        slotIndex: 2,
        rowType: 'first'
      });
    });
  });

  describe('When a player tries to place a card in an occupied slot', () => {
    it('should throw an error', async () => {
      // Arrange - Place a card first
      await john.mutation(api.mutations.playCard.endpoint, {
        matchId,
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 2
      });

      // Act & Assert
      await expect(john.mutation(api.mutations.playCard.endpoint, {
        matchId,
        cardId: 'card-456',
        rowType: 'first',
        slotIndex: 2
      })).rejects.toThrow();
    });
  });

  describe('When a player tries to place a card in a non-active match', () => {
    it('should throw an error', async () => {
      // Arrange - Set match status to finished
      await asAdmin.run(ctx => ctx.db.patch(matchId, {status: 'finished'}));

      // Act & Assert
      await expect(john.mutation(api.mutations.playCard.endpoint, {
        matchId,
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 2
      })).rejects.toThrow();
    });
  });
});
