import {LoadMatchByIdWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchByIdWebPresenter";
import {Match} from "@/src/server/Gaming/domain/Match/Match";
import {Id} from "@/convex/_generated/dataModel";

describe('LoadMatchByIdWebPresenter', () => {
  let presenter: LoadMatchByIdWebPresenter;

  beforeEach(() => {
    presenter = new LoadMatchByIdWebPresenter();
  });

  describe('When displaying a match', () => {
    it('should set match data', () => {
      // Arrange
      const match = Match.fromSnapshot({id: 'm1', gameId: 'g1', players: ['u1','u2'], status: 'setup'});

      // Act
      presenter.display(match, 'u1');

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data).toEqual({
        id: 'm1',
        status: 'setup',
        players: ['u1','u2'],
        isWinner: false,
        gameId: 'g1' as Id<'games'>,
      });
    });
  });

  describe('When displaying an error', () => {
    it('should set the error message', () => {
      // Arrange
      const error = new Error('Repository error');

      // Act
      presenter.displayError(error);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.error).toBe(error.message);
    });
  });
});
