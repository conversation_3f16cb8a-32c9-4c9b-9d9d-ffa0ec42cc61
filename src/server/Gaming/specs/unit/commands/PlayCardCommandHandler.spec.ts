import {PlayCardCommandHandler} from '@/src/server/Gaming/application/commands/Match/PlayCard/PlayCardCommandHandler';
import {Match} from '@/src/server/Gaming/domain/Match/Match';
import {InMemoryMatchRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchRepository';

describe('PlayCardCommandHandler', () => {
  let matchRepository: InMemoryMatchRepository;
  let handler: PlayCardCommandHandler;

  beforeEach(() => {
    matchRepository = new InMemoryMatchRepository();
    handler = new PlayCardCommandHandler(matchRepository);
  });

  describe('When playing a card in active match', () => {
    it('should place card on board and update match state', async () => {
      // Arrange
      const match = Match.create({
        id: 'match-123',
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active'
      });
      await matchRepository.save(match);

      // Act
      await handler.handle({
        matchId: 'match-123',
        cardId: 'card-789',
        rowType: 'first',
        slotIndex: 2,
        userId: 'player1'
      });

      // Assert
      const updatedMatch = await matchRepository.findById('match-123');
      expect(updatedMatch).toBeDefined();
      // This test will initially fail because Match and PlayCardCommandHandler don't exist yet
    });
  });

  describe('When playing card in non-active match', () => {
    it('should throw error for invalid match state', async () => {
      // Arrange
      const match = Match.create({
        id: 'match-123',
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'finished'
      });
      await matchRepository.save(match);

      // Act

      // Assert
      await expect(handler.handle({
        matchId: 'match-123',
        cardId: 'card-789',
        rowType: 'first',
        slotIndex: 2,
        userId: 'player1'
      })).rejects.toThrow();
    });
  });
});