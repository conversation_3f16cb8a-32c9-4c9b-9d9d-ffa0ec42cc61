import {PlayCardCommandHandler} from '@/src/server/Gaming/application/commands/Match/PlayCard/PlayCardCommandHandler';
import {Match} from '@/src/server/Gaming/domain/Match/Match';
import {InMemoryMatchRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchRepository';

describe('PlayCardCommandHandler', () => {
  let matchRepository: InMemoryMatchRepository;
  let handler: PlayCardCommandHandler;

  beforeEach(() => {
    matchRepository = new InMemoryMatchRepository();
    handler = new PlayCardCommandHandler(matchRepository);
  });

  describe('When playing a card in active match', () => {
    it('should place card on board and update match state', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active'
      });
      const matchId = await matchRepository.save(match);

      // Act
      await handler.handle({
        matchId,
        cardId: 'card-789',
        rowType: 'first',
        slotIndex: 2,
        userId: 'player1'
      });

      // Assert
      const updatedMatch = await matchRepository.findById(matchId);
      expect(updatedMatch).toBeDefined();

      const boardState = updatedMatch!.getBoardState();
      expect(boardState).toBeDefined();
      expect(boardState!.player1Board.firstRow[2]).toEqual({
        cardId: 'card-789',
        placedAt: expect.any(String),
        slotIndex: 2,
        rowType: 'first'
      });
    });
  });

  describe('When playing card in non-active match', () => {
    it('should throw error for invalid match state', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'finished'
      });
      const matchId = await matchRepository.save(match);

      // Act

      // Assert
      await expect(handler.handle({
        matchId,
        cardId: 'card-789',
        rowType: 'first',
        slotIndex: 2,
        userId: 'player1'
      })).rejects.toThrow();
    });
  });
});